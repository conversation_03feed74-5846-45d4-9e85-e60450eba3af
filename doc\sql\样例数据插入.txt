-- =================================================================
-- Digital Human - End-to-End Demo Data Initialization Script (Full & Unabridged Version)
-- =================================================================
-- 描述: 本脚本为 dh_user_profile 和 dh_tripplanner 两个数据库
--       插入一套完整、互相关联的模拟数据，用于端侧呈现和功能演示。
--       新增了家庭关系记忆和App专属旅行画像，数据更丰富、更智能。
--       脚本可重复执行。
-- =================================================================

-- *****************************************************************
-- Part 1: 为 `dh_user_profile` 库插入数据
-- *****************************************************************
USE `dh_user_profile`;

-- 清理旧数据，方便重复执行 (TRUNCATE会重置自增ID)
SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE TABLE `user_vehicle_bindings`;
TRUNCATE TABLE `user_summaries`;
TRUNCATE TABLE `user_memories`;
TRUNCATE TABLE `credentials`;
TRUNCATE TABLE `vehicles`;
TRUNCATE TABLE `users`;
SET FOREIGN_KEY_CHECKS = 1;


-- 1.1 创建用户 (一家三口)
INSERT INTO `users` (`id`, `nickname`, `avatar_url`, `status`) VALUES
(1, '李伟', 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2', 'ACTIVE'),
(2, '王静', 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2', 'ACTIVE'),
(3, '李小乐', 'https://images.pexels.com/photos/35537/child-children-girl-happy.jpg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2', 'ACTIVE');

-- 1.2 创建车辆信息 (北汽极狐)
INSERT INTO `vehicles` (`vin`, `license_plate`, `brand`, `model`, `activated_at`) VALUES
('DHP1ARCFOXN51XYZ1', '京A·DH123', '北汽极狐', 'N51', '2024-05-20 10:00:00');

-- 1.3 创建用户与车辆的强绑定关系
INSERT INTO `user_vehicle_bindings` (`user_id`, `vehicle_vin`, `role`, `status`, `bind_at`) VALUES
(1, 'DHP1ARCFOXN51XYZ1', 'OWNER', 'ACTIVE', '2024-05-20 10:01:00'),
(2, 'DHP1ARCFOXN51XYZ1', 'FAMILY', 'ACTIVE', '2024-05-21 11:30:00'),
(3, 'DHP1ARCFOXN51XYZ1', 'FAMILY', 'ACTIVE', '2024-05-21 11:30:00');

-- 1.4 创建登录凭证 (假设都通过车机系统认证)
INSERT INTO `credentials` (`user_id`, `provider`, `identifier`) VALUES
(1, 'vehicle_os', 'ARCFOX_UID_LIWEI_001'),
(2, 'vehicle_os', 'ARCFOX_UID_WANGJING_002'),
(3, 'vehicle_os', 'ARCFOX_UID_LIXIAOLE_003');

-- 1.5 创建AI长期记忆 (包含通用记忆和家庭关系记忆)
-- 李伟 (车主) 的记忆
INSERT INTO `user_memories` (`user_id`, `memory_content`) VALUES
(1, '用户喜欢驾驶，对车辆的智能辅助驾驶功能很感兴趣。'),
(1, '用户是一名摄影爱好者，尤其喜欢拍摄古建筑和风景。'),
(1, '用户王静(ID:2)是该用户的妻子。'),
(1, '用户李小乐(ID:3)是该用户的儿子。');
-- 王静 (妻子) 的记忆
INSERT INTO `user_memories` (`user_id`, `memory_content`) VALUES
(2, '用户偏爱有历史文化底蕴的街区和美食。'),
(2, '用户在规划家庭出游时，会优先考虑行程的舒适度和是否适合孩子。'),
(2, '用户李伟(ID:1)是该用户的丈夫。'),
(2, '用户李小乐(ID:3)是该用户的儿子。');
-- 李小乐 (孩子) 的记忆
INSERT INTO `user_memories` (`user_id`, `memory_content`) VALUES
(3, '用户今年4岁。'),
(3, '用户喜欢动物园和大型游乐场。'),
(3, '用户李伟(ID:1)是该用户的父亲。'),
(3, '用户王静(ID:2)是该用户的母亲。');

-- 1.6 创建通用用户画像摘要 (AI对记忆的总结)
INSERT INTO `user_summaries` (`user_id`, `summary`, `keywords`, `model_version`) VALUES
(1, '李伟是一位热爱驾驶和科技的摄影爱好者，其家庭成员包括妻子王静(ID:2)和儿子李小乐(ID:3)。他对车辆的智能功能有很高要求，旅行时倾向于探索和拍摄有历史感的建筑及壮丽的自然风光。', '["驾驶", "科技爱好者", "摄影", "古建筑", "家庭"]', 'v2.0-summarizer'),
(2, '王静是注重家庭体验的旅行者，丈夫是李伟(ID:1)，儿子是李小乐(ID:3)。她钟爱美食和充满历史气息的文化街区。在规划行程时，舒适性和亲子适宜度是她首要考虑的因素。', '["家庭出游", "美食家", "文化历史", "舒适", "亲子"]', 'v2.0-summarizer'),
(3, '李小乐是一名4岁的儿童，父亲是李伟(ID:1)，母亲是王静(ID:2)。他对世界充满好奇，动物园和大型游乐场等充满趣味和互动性的地方对他有很强的吸引力。', '["4岁", "儿童", "亲子", "游乐场", "动物园"]', 'v2.0-summarizer');


-- *****************************************************************
-- Part 2: 为 `dh_tripplanner` 库插入数据
-- *****************************************************************
USE `dh_tripplanner`;

-- 清理旧数据，方便重复执行
SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE TABLE `vlog_media_items`;
TRUNCATE TABLE `ai_vlogs`;
TRUNCATE TABLE `user_media`;
TRUNCATE TABLE `user_app_settings`;
TRUNCATE TABLE `user_trip_stats`;
TRUNCATE TABLE `itinerary_day_pois`;
TRUNCATE TABLE `itinerary_days`;
TRUNCATE TABLE `itinerary_tags`;
TRUNCATE TABLE `itineraries`;
TRUNCATE TABLE `pois`;
TRUNCATE TABLE `user_travel_profiles`;
TRUNCATE TABLE `tags`;
TRUNCATE TABLE `poi_types`;
TRUNCATE TABLE `status_definitions`;
SET FOREIGN_KEY_CHECKS = 1;


-- 2.1 插入字典表数据
INSERT INTO `poi_types` (`id`, `type_key`, `display_name`, `icon_name`) VALUES (1, 'ATTRACTION', '景点', 'icon-attraction'), (2, 'RESTAURANT', '美食', 'icon-restaurant'), (3, 'HOTEL', '酒店', 'icon-hotel'), (4, 'SHOPPING', '购物', 'icon-shopping'), (5, 'OTHER', '其他', 'icon-other');
INSERT INTO `status_definitions` (`id`, `status_key`, `scope`, `display_name`) VALUES (1, 'DRAFT', 'ITINERARY', '草稿'), (2, 'ACTIVE', 'ITINERARY', '进行中'), (3, 'COMPLETED', 'ITINERARY', '已完成'), (4, 'ARCHIVED', 'ITINERARY', '已归档'), (5, 'PENDING', 'VLOG', '待处理'), (6, 'PROCESSING', 'VLOG', '生成中'), (7, 'COMPLETED', 'VLOG', '已完成'), (8, 'FAILED', 'VLOG', '生成失败');
INSERT INTO `tags` (`id`, `name`, `category`) VALUES (1, '历史文化', '旅行主题'), (2, '自然风光', '旅行主题'), (3, '亲子出行', '适合人群'), (4, '美食之旅', '旅行主题'), (5, '摄影采风', '旅行主题'), (6, '城市漫步', '旅行方式');

-- 2.2 插入App专属旅行画像
INSERT INTO `user_travel_profiles` (`user_id`, `travel_style`, `accommodation_pref`, `transportation_pref`, `travel_summary`, `travel_keywords`) VALUES
(1, 'ADVENTURE', '["精品酒店", "特色民宿"]', '["自驾"]', '作为一名自驾爱好者，李伟偏爱探索人迹罕至的自然风光和有历史感的目的地。他喜欢携带专业设备进行摄影采风，对行程的自由度要求较高，不介意具有挑战性的''冒险''风格旅行。', '["自驾", "摄影采风", "自然风光", "历史探索", "冒险"]'),
(2, 'RELAXED', '["连锁品牌", "度假村"]', '["高铁", "飞机"]', '王静是家庭旅行的组织者，她极度重视行程的舒适度和安全性。她偏爱有文化氛围的城市漫步，喜欢品尝当地特色美食。对住宿的要求较高，精品酒店是她的首选。', '["家庭出游", "舒适", "美食之旅", "精品酒店", "城市漫步"]'),
(3, 'FAMILY', '["主题酒店"]', '["自驾"]', '李小乐的旅行画像核心是“新奇有趣”。他对充满卡通元素的主题酒店、能尽情玩耍的乐园和可以与小动物互动的公园有极高的兴趣。行程节奏需轻松缓慢。', '["主题乐园", "动物互动", "节奏轻松", "新奇"]');

-- 2.3 插入POI数据 (完整版)
INSERT INTO `pois` (`id`, `type_id`, `name`, `address`, `latitude`, `longitude`, `description`, `images`, `narrations`, `rating`) VALUES
(1, 1, '故宫博物院', '北京市东城区景山前街4号', 39.9163, 116.3972, '故宫，又称紫禁城，是明清两代的皇家宫殿，是中国古代宫廷建筑之精华，也是世界上现存规模最大、保存最为完整的木质结构古建筑之一。', '["https://images.pexels.com/photos/103597/pexels-photo-103597.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"]', '[{"lang":"zh-CN", "style":"standard", "title":"故宫概览", "content":"欢迎来到故宫博物院。这里曾是明清两代24位皇帝的家，见证了五百余年的历史风云。漫步在雄伟的殿宇和精致的园林之间，您将触摸到中华文明的脉搏。请注意，故宫实行自南向北单向参观，出口为神武门。"}]', 4.8),
(2, 1, '天坛公园', '北京市东城区天坛东里甲1号', 39.8826, 116.4063, '天坛是明清两代皇帝祭祀皇天、祈五谷丰登的场所。它以其严谨的建筑布局、奇特的建筑构造和瑰丽的建筑装饰著称于世。', '["https://images.pexels.com/photos/13101680/pexels-photo-13101680.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"]', '[{"lang":"zh-CN", "style":"standard", "title":"天坛祈年殿", "content":"您眼前的这座宏伟建筑就是祈年殿，天坛的标志。它的圆形攒尖顶设计，象征着‘天圆地方’的宇宙观。整座大殿未使用一根长钉，完全依靠木质卯榫结构支撑，是中国古代建筑智慧的结晶。"}]', 4.7),
(3, 1, '八达岭长城', '北京市延庆区G6京藏高速58号出口', 40.3544, 116.0041, '八达岭长城是明长城中保存最好、也最具代表性的地段，是明代重要的军事关隘和首都北京的重要屏障。这里地势险要，是游客感受长城雄伟壮丽的绝佳地点。', '["https://images.pexels.com/photos/388830/pexels-photo-388830.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"]', '[{"lang":"zh-CN", "style":"standard", "title":"不到长城非好汉", "content":"八达岭长城，以其宏伟的景观、完善的设施和深厚的文化历史内涵而著称于世。攀登其上，您不仅可以体验‘不到长城非好汉’的豪迈，更能亲身感受到中华民族坚韧不拔的伟大精神。"}]', 4.8),
(4, 1, '三坊七巷', '福州市鼓楼区南后街', 26.0869, 119.2974, '三坊七巷起于晋，完善于唐五代，至明清鼎盛，是国内现存规模较大、保护较为完整的历史文化街区，被誉为“中国城市里坊制度活化石”和“中国明清建筑博物馆”。', '["https://images.pexels.com/photos/14888514/pexels-photo-14888514.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"]', '[{"lang":"zh-CN", "style":"standard", "title":"漫步三坊七巷", "content":"三坊七巷，一片保存完好的明清古建筑群，是福州历史的缩影。走在青石板路上，两侧的白墙黛瓦间，藏着林则徐、严复、冰心等无数名人的故事。这里不仅是建筑的博物馆，更是福州文脉的延续。"}]', 4.6),
(5, 2, '同利肉燕老铺', '福州市鼓楼区南后街106号', 26.0875, 119.2970, '福州著名的小吃店，以其独特的肉燕而闻名。肉燕的皮是用精选的猪后腿肉捶打而成，薄如纸，韧而有劲，包裹着鲜美的肉馅，是体验福州风味的必到之处。', '["https://images.pexels.com/photos/2347311/pexels-photo-2347311.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"]', '[{"lang":"zh-CN", "style":"standard", "title":"品尝福州肉燕", "content":"“无燕不成宴，无燕不成年”。在福州，肉燕不仅仅是一道小吃，更是一种文化符号。同利家的肉燕，皮Q馅香，汤鲜味美，是代代福州人心中不变的家乡味道。"}]', 4.5),
(6, 1, '福州西湖公园', '福州市鼓楼区湖滨路70号', 26.0966, 119.2917, '福州西湖公园至今有1700多年的历史，是福州保留最完整的一座古典园林。其景色秀丽，历史悠久，是市民休闲游乐的好去处。', '["https://images.pexels.com/photos/33041/antelope-canyon-lower-canyon-arizona.jpg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"]', '[{"lang":"zh-CN", "style":"standard", "title":"西湖倩影", "content":"福州西湖，宛如一颗镶嵌在城市中心的璀璨明珠。漫步在柳浪闻莺的堤岸，或是泛舟湖上，都能感受到古典园林的诗情画意。这里是远离城市喧嚣，享受片刻宁静的绝佳之地。"}]', 4.6),
(7, 1, '外滩', '上海市黄浦区中山东一路', 31.2389, 121.4883, '外滩是上海的标志性景观，全长1.5公里，沿途矗立着52幢风格迥异的古典复兴大楼，素有外滩万国建筑博览群之称，是观赏浦江两岸风光的最佳地点。', '["https://images.pexels.com/photos/2827735/pexels-photo-2827735.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"]', '[{"lang":"zh-CN", "style":"standard", "title":"漫步外滩", "content":"欢迎来到外滩，上海的城市客厅。您的左手边，是见证了百年风云的万国建筑博览群；您的右手边，是代表着中国发展速度的陆家嘴天际线。新与旧、历史与未来，在此刻交相辉映。"}]', 4.8),
(8, 1, '豫园', '上海市黄浦区福佑路168号', 31.2269, 121.4891, '豫园是著名的江南古典园林，始建于明代，距今已有四百余年历史。园内亭台楼阁、假山池沼，设计精巧、布局细腻，是“奇秀甲于东南”的园林瑰宝。', '["https://images.pexels.com/photos/10186536/pexels-photo-10186536.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"]', '[{"lang":"zh-CN", "style":"standard", "title":"豫园之美", "content":"豫园之美，在于其移步换景的精巧布局。您将穿过曲径通幽的小道，欣赏到被誉为镇园之宝的玉玲珑，以及点春堂、大假山等著名景点。这里是繁华都市中一处难得的古典静谧之地。"}]', 4.6),
(9, 1, '上海迪士尼乐园', '上海市浦东新区川沙镇黄赵路310号', 31.1446, 121.6582, '中国内地首座迪士尼主题乐园，拥有七大主题园区和众多独有的游乐项目。无论是经典的城堡，还是刺激的创极速光轮，都能为所有年龄段的游客带来奇妙的体验。', '["https://images.pexels.com/photos/1684151/pexels-photo-1684151.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"]', '[{"lang":"zh-CN", "style":"kid", "title":"奇妙的一天", "content":"小朋友们，欢迎来到充满魔法的迪士尼乐园！准备好和米奇米妮一起玩耍了吗？快看，远处就是奇幻童话城堡，里面住着好多位公主哦！让我们一起去探险，点亮心中的奇梦吧！"}]', 4.9);

-- 2.4 创建行程主表数据 (status_id=3 代表已完成)
INSERT INTO `itineraries` (`id`, `user_id`, `status_id`, `title`, `city_name`, `total_days`) VALUES
(1, 1, 3, '北京3日摄影采风之旅', '北京', 3),
(2, 2, 3, '福州3日家庭休闲游', '福州', 3),
(3, 3, 3, '上海3日亲子奇妙乐园行', '上海', 3);

-- 2.5 关联行程与标签
INSERT INTO `itinerary_tags` (`itinerary_id`, `tag_id`) VALUES (1, 1), (1, 5), (2, 1), (2, 3), (2, 4), (3, 3), (3, 6);

-- 2.6 创建行程日详情
INSERT INTO `itinerary_days` (`id`, `itinerary_id`, `day_number`, `summary`) VALUES (1, 1, 1, '故宫深度游'), (2, 1, 2, '登长城做好汉'), (3, 1, 3, '天坛祈福'), (4, 2, 1, '漫步三坊七巷'), (5, 2, 2, '西湖公园泛舟'), (6, 2, 3, '品尝地道小吃'), (7, 3, 1, '外滩观光'), (8, 3, 2, '豫园寻古'), (9, 3, 3, '迪士尼奇妙日');

-- 2.7 创建每日行程的POI安排
INSERT INTO `itinerary_day_pois` (`itinerary_day_id`, `poi_id`, `sequence`) VALUES (1, 1, 1), (2, 3, 1), (3, 2, 1), (4, 4, 1), (5, 6, 1), (6, 5, 1), (7, 7, 1), (8, 8, 1), (9, 9, 1);

-- 2.8 创建用户行程统计数据
INSERT INTO `user_trip_stats` (`user_id`, `trip_count`, `city_count`, `total_mileage`, `total_days`) VALUES
(1, 1, 1, 55.8, 3),
(2, 1, 1, 32.1, 3),
(3, 1, 1, 45.5, 3);

-- 2.9 创建App专属设置
INSERT INTO `user_app_settings` (`user_id`, `narration_enabled`, `in_poi_guide_enabled`, `proactive_recommend_enabled`) VALUES
(1, TRUE, TRUE, TRUE),
(2, TRUE, TRUE, FALSE),
(3, TRUE, FALSE, TRUE);


-- 为不同App创建专属的收藏标签
-- 旅游搭子 (dh_tripplanner) 的标签
INSERT INTO `favorite_tags` (`application_source`, `tag_name`, `weight_multiplier`) VALUES
('dh_tripplanner', '住宿偏好', 1.5),
('dh_tripplanner', '餐厅黑名单', 2.0),
('dh_tripplanner', '梦想目的地', 1.2);

-- AI Lab (dh_ailab) 的标签
INSERT INTO `favorite_tags` (`application_source`, `tag_name`, `weight_multiplier`) VALUES
('dh_ailab', 'Prompt技巧', 1.3),
('dh_ailab', '重要结论', 1.8),
('dh_ailab', '待验证想法', 1.0);

-- 插入一条用户收藏的示例数据
-- 假设用户李伟(ID:1) 收藏了他在某次行程规划中关于酒店的一段建议
INSERT INTO `user_favorites` (`user_id`, `interaction_id`, `tag_id`, `selected_content`, `user_notes`) VALUES
(1, 'uuid-planner-xyz-789', 1, '明白了，您似乎偏爱安静且带有免费洗衣房的连锁酒店。', '这个总结很准，下次直接按这个标准找酒店！');


-- =================================================================
-- Digital Human - AI Lab & VPA Center Full Data Reset Script
-- Version: Final
-- =================================================================
-- 描述: 本脚本将彻底清空 dh_ailab 数据库中的所有数据，
--       然后插入一套全新的、完整的模拟数据，用于功能演示。
--       此脚本具有破坏性，会删除所有现有数据，请谨慎执行。
--       可以重复执行以恢复到初始演示状态。
-- =================================================================

-- 切换到目标 Schema
USE `dh_ailab`;

-- -----------------------------------------------------
-- 步骤 1: 彻底清空所有表的数据
-- -----------------------------------------------------
-- 暂时禁用外键约束，以便能按任意顺序清空表
SET FOREIGN_KEY_CHECKS = 0;

TRUNCATE TABLE `user_unlocked_skills`;
TRUNCATE TABLE `user_vpa_instances`;
TRUNCATE TABLE `ai_skills`;
TRUNCATE TABLE `vpa_personas`;
TRUNCATE TABLE `ai_skill_categories`;
TRUNCATE TABLE `vpa_generation_styles`;
TRUNCATE TABLE `status_definitions`;

-- 操作完成后，重新启用外键约束
SET FOREIGN_KEY_CHECKS = 1;


-- -----------------------------------------------------
-- 步骤 2: 为所有表插入全新的样例数据
-- -----------------------------------------------------

-- 2.1 插入字典表数据
INSERT INTO `ai_skill_categories` (`id`, `category_key`, `name`, `display_order`) VALUES
(1, 'family', '亲子家庭', 10),
(2, 'travel', '随车出行', 20),
(3, 'entertainment', '影音娱乐', 30),
(4, 'productivity', '智能助手', 40);

INSERT INTO `vpa_generation_styles` (`id`, `style_key`, `name`, `description`) VALUES
(1, 'cartoon', '卡通', '生成色彩鲜艳、线条流畅的卡通风格形象。'),
(2, 'realistic', '写实', '生成接近真人质感、光影细节丰富的写实风格形象。'),
(3, 'cyberpunk', '赛博朋克', '生成充满未来科技感的霓虹光效与机械元素形象。');

INSERT INTO `status_definitions` (`id`, `status_key`, `scope`, `display_name`) VALUES
(1, 'ACTIVE', 'VPA_INSTANCE', '可用'),
(2, 'GENERATING', 'VPA_INSTANCE', '制作中'),
(3, 'FAILED', 'VPA_INSTANCE', '制作失败');

-- 2.2 插入AI技能 (使用结构化的JSON content)
INSERT INTO `ai_skills` (`id`, `skill_key`, `category_id`, `name`, `content`, `unlock_cost_points`) VALUES
(1, 'parent_child_school', 1, '亲子学堂', '{
  "title": "亲子学堂",
  "subtitle": "专为家庭场景设计的智能助手",
  "cover_image_url": "https://images.pexels.com/photos/3931603/pexels-photo-3931603.jpeg?auto=compress&cs=tinysrgb&w=800",
  "details": "提供住车保姆、启蒙早教、学前辅导等一系列服务，它能通过哭声检测自动安抚宝宝，也能在旅途中为孩子讲解百科知识，成为您育儿路上的好帮手。",
  "usage_examples": ["启动住车保姆模式", "给我讲个睡前故事", "1加1等于几？"],
  "unlock_benefits": ["永久激活该技能", "技能中心可随时调用", "后续更新永久免费"]
}', 500),

(2, 'trip_guide', 2, '随车导游', '{
  "title": "随车导游",
  "subtitle": "说走就走的旅行助手",
  "cover_image_url": "https://images.pexels.com/photos/347141/pexels-photo-347141.jpeg?auto=compress&cs=tinysrgb&w=800",
  "details": "支持文旅特色路线定制、AI导游生成攻略与景点解说、AI-Vlog编辑与分享等，满足用户出行规划、记录与分享的全链路需求。",
  "usage_examples": ["帮我规划一个北京三日游", "介绍一下故宫", "生成我的旅行Vlog"],
  "unlock_benefits": ["永久激活该技能", "路线规划与Vlog生成", "后续更新永久免费"]
}', 200),

(3, 'exclusive_copilot', 4, '专属副驾', '{
  "title": "专属副驾",
  "subtitle": "懂你情绪的聊天搭子",
  "cover_image_url": "https://images.pexels.com/photos/163036/mario-luigi-yoschi-figures-163036.jpeg?auto=compress&cs=tinysrgb&w=800",
  "details": "提供陪伴式聊天解闷、支持鼓励、安慰与拟人互动。它能记住你的喜好，理解你的情绪，成为您忠实的伙伴。",
  "usage_examples": ["我今天心情不太好", "给我讲个笑话吧", "我最喜欢的歌手是谁？"],
  "unlock_benefits": ["永久激活该技能", "深度个性化记忆", "后续更新永久免费"]
}', 100);

-- 2.3 插入用户已解锁的技能 (李伟解锁了亲子学堂和随车导游)
INSERT INTO `user_unlocked_skills` (`user_id`, `skill_id`, `is_enabled`) VALUES
(1, 1, TRUE),  -- 李伟解锁了亲子学堂，并开启了主动服务
(1, 2, FALSE); -- 李伟解锁了随车导游，但关闭了主动服务

-- 2.4 插入预设VPA人设
INSERT INTO `vpa_personas` (`id`, `name`, `thumbnail_url`, `asset_id`, `actions`, `is_default`) VALUES
(1, '默认', 'https://raw.githubusercontent.com/PKU-YuanGroup/ChatLaw/main/assets/robot.png', 'persona_asset_001', '{"idle": "action_idle_001", "greet": "action_greet_001", "think": "action_think_001"}', TRUE),
(2, '后浪', 'https://raw.githubusercontent.com/PKU-YuanGroup/ChatLaw/main/assets/zhanzhang.png', 'persona_asset_002', '{"idle": "action_idle_002", "greet": "action_greet_002", "think": "action_think_002"}', FALSE),
(3, '小理', 'https://raw.githubusercontent.com/PKU-YuanGroup/ChatLaw/main/assets/chengxuyuan.png', 'persona_asset_003', '{"idle": "action_idle_003", "greet": "action_greet_003", "think": "action_think_003"}', FALSE);

-- 2.5 插入用户的VPA形象实例
-- 李伟 (ID:1) 有两个形象: 一个是当前使用的UGC形象，一个是备用的预设形象
INSERT INTO `user_vpa_instances` (`id`, `user_id`, `nickname`, `status_id`, `is_currently_active`, `asset_id`, `actions`, `thumbnail_url`, `custom_wake_word`, `custom_response`, `generation_source`, `base_persona_id`, `generation_style_id`, `generation_prompt`) VALUES
(1, 1, '机甲战士', 1, TRUE, 'ugc_asset_lw_001', '{"idle": "ugc_idle_a", "greet": "ugc_greet_a", "think": "ugc_think_a", "success": "ugc_success_a"}', 'https://images.pexels.com/photos/1036808/pexels-photo-1036808.jpeg?auto=compress&cs=tinysrgb&w=400', '小狐狸', '主人，我在', 'UGC', NULL, 3, '一个穿着高科技装甲的男孩，赛博朋克风格，蓝色护目镜'),
(2, 1, '默认助手', 1, FALSE, 'persona_asset_001', '{"idle": "action_idle_001", "greet": "action_greet_001", "think": "action_think_001"}', 'https://raw.githubusercontent.com/PKU-YuanGroup/ChatLaw/main/assets/robot.png', NULL, NULL, 'PRESET', 1, NULL, NULL);

-- 王静 (ID:2) 有一个形象
INSERT INTO `user_vpa_instances` (`user_id`, `nickname`, `status_id`, `is_currently_active`, `asset_id`, `actions`, `thumbnail_url`, `generation_source`, `base_persona_id`) VALUES
(2, '潮流玩家', 1, TRUE, 'persona_asset_002', '{"idle": "action_idle_002", "greet": "action_greet_002", "think": "action_think_002"}', 'https://raw.githubusercontent.com/PKU-YuanGroup/ChatLaw/main/assets/zhanzhang.png', 'PRESET', 2);

-- 李小乐 (ID:3) 也有一个爸爸为他做的卡通形象
INSERT INTO `user_vpa_instances` (`user_id`, `nickname`, `status_id`, `is_currently_active`, `asset_id`, `actions`, `thumbnail_url`, `generation_source`, `generation_style_id`, `generation_prompt`) VALUES
(3, '螺旋桨宝宝', 1, TRUE, 'ugc_asset_lxl_001', '{"idle": "ugc_idle_b", "greet": "ugc_greet_b", "think": "ugc_think_b"}', 'https://images.pexels.com/photos/207823/pexels-photo-207823.jpeg?auto=compress&cs=tinysrgb&w=400', 'UGC', 1, '一个可爱的卡通小机器人，戴着一顶螺旋桨帽子');