"""
测试各个节点是否真正使用了LLM
通过监控LLM调用来验证节点实现
"""

import asyncio
import json
import httpx
import time
from typing import List, Dict, Any
import re

# API配置
BASE_URL = "http://localhost:8000"
HEADERS = {"Content-Type": "application/json"}

class LLMUsageAnalyzer:
    def __init__(self):
        self.base_url = BASE_URL
        self.headers = HEADERS
        self.llm_calls = []
        self.step_timings = {}
    
    async def test_with_llm_monitoring(self, query: str, user_id: str = "test_llm_monitor"):
        """
        测试旅行规划并监控LLM使用情况
        """
        print("=" * 60)
        print("🔍 监控LLM使用情况测试")
        print("=" * 60)
        
        url = f"{self.base_url}/api/travel/v2/plan/stream"
        
        request_data = {
            "query": query,
            "user_id": user_id
        }
        
        print(f"📝 请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
        print(f"🌐 请求URL: {url}")
        print("\n📡 SSE事件流分析:")
        print("-" * 40)
        
        events = []
        step_start_times = {}
        step_durations = {}
        
        async with httpx.AsyncClient(timeout=120.0) as client:
            async with client.stream("POST", url, json=request_data, headers=self.headers) as response:
                if response.status_code != 200:
                    print(f"❌ 请求失败: {response.status_code}")
                    print(f"错误信息: {await response.atext()}")
                    return None
                
                event_count = 0
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        event_count += 1
                        try:
                            json_data = line[6:]  # 移除 "data: " 前缀
                            if json_data.strip() == "":
                                continue
                                
                            event = json.loads(json_data)
                            events.append(event)
                            
                            # 记录时间
                            current_time = time.time()
                            
                            # 显示事件信息并分析时间
                            event_type = event.get("event", "unknown")
                            if event_type == "step_start":
                                step_name = event.get("data", {}).get("step_name", "unknown")
                                title = event.get("data", {}).get("title", "")
                                step_start_times[step_name] = current_time
                                print(f"[{event_count:2d}] 🚀 开始: {step_name} - {title}")
                                print(f"     ⏰ 开始时间: {time.strftime('%H:%M:%S', time.localtime(current_time))}")
                            
                            elif event_type == "step_end":
                                step_name = event.get("data", {}).get("step_name", "unknown")
                                status = event.get("data", {}).get("status", "unknown")
                                result = event.get("data", {}).get("result", {})
                                content = result.get("content", "")
                                
                                # 计算执行时间
                                if step_name in step_start_times:
                                    duration = current_time - step_start_times[step_name]
                                    step_durations[step_name] = duration
                                    duration_str = f"{duration:.2f}秒"
                                else:
                                    duration_str = "未知"
                                
                                print(f"[{event_count:2d}] ✅ 完成: {step_name} - {status}")
                                print(f"     ⏱️  执行时间: {duration_str}")
                                if content:
                                    print(f"     💡 结果: {content}")
                                
                                # 分析是否可能使用了LLM
                                self._analyze_llm_usage(step_name, duration if step_name in step_start_times else 0, content)
                            
                            elif event_type == "complete":
                                print(f"[{event_count:2d}] 🎉 规划完成!")
                                data = event.get("data", {})
                                if "destinations" in data:
                                    print(f"     🏙️  目的地: {data.get('destinations', [])}")
                                if "total_days" in data:
                                    print(f"     📅 天数: {data.get('total_days', 0)}")
                            
                            elif event_type == "eos":
                                print(f"[{event_count:2d}] 🔚 流结束")
                                break
                                
                        except json.JSONDecodeError as e:
                            print(f"❌ JSON解析错误: {e}")
                            print(f"原始数据: {line}")
        
        # 分析结果
        self._analyze_results(step_durations)
        return events
    
    def _analyze_llm_usage(self, step_name: str, duration: float, content: str):
        """
        分析步骤是否使用了LLM
        """
        # LLM使用的判断标准
        llm_indicators = {
            "duration_threshold": 2.0,  # 超过2秒可能使用了LLM
            "content_patterns": [
                r"分析.*完成",
                r"识别.*目的地",
                r"理解.*需求",
                r"推理.*结果"
            ],
            "known_llm_steps": [
                "core_intent_analysis"  # 已知使用LLM的步骤
            ]
        }
        
        likely_llm = False
        reasons = []
        
        # 1. 检查执行时间
        if duration > llm_indicators["duration_threshold"]:
            likely_llm = True
            reasons.append(f"执行时间较长({duration:.2f}秒)")
        
        # 2. 检查内容模式
        for pattern in llm_indicators["content_patterns"]:
            if re.search(pattern, content):
                likely_llm = True
                reasons.append(f"内容匹配LLM模式: {pattern}")
                break
        
        # 3. 检查已知LLM步骤
        if step_name in llm_indicators["known_llm_steps"]:
            likely_llm = True
            reasons.append("已知LLM步骤")
        
        # 记录分析结果
        analysis = {
            "step_name": step_name,
            "duration": duration,
            "content": content,
            "likely_llm": likely_llm,
            "reasons": reasons
        }
        
        self.llm_calls.append(analysis)
        
        # 实时显示分析
        if likely_llm:
            print(f"     🤖 可能使用LLM: {', '.join(reasons)}")
        else:
            print(f"     ⚡ 可能未使用LLM: 执行时间短({duration:.2f}秒)")
    
    def _analyze_results(self, step_durations: Dict[str, float]):
        """
        分析整体结果
        """
        print("\n" + "=" * 60)
        print("📊 LLM使用情况分析报告")
        print("=" * 60)
        
        # 按执行时间排序
        sorted_steps = sorted(step_durations.items(), key=lambda x: x[1], reverse=True)
        
        print("\n⏱️  步骤执行时间排序:")
        print("-" * 40)
        for step_name, duration in sorted_steps:
            status = "🤖 可能使用LLM" if duration > 2.0 else "⚡ 可能未使用LLM"
            print(f"{status}: {step_name} - {duration:.2f}秒")
        
        # 分析可疑的快速步骤
        fast_steps = [(name, duration) for name, duration in step_durations.items() if duration < 0.5]
        if fast_steps:
            print(f"\n⚠️  执行过快的步骤 (< 0.5秒):")
            print("-" * 40)
            for step_name, duration in fast_steps:
                print(f"   • {step_name}: {duration:.2f}秒")
                # 查找对应的分析
                analysis = next((a for a in self.llm_calls if a["step_name"] == step_name), None)
                if analysis and not analysis["likely_llm"]:
                    print(f"     ❌ 疑似硬编码结果")
        
        # 统计
        total_steps = len(step_durations)
        likely_llm_steps = len([a for a in self.llm_calls if a["likely_llm"]])
        likely_hardcoded_steps = total_steps - likely_llm_steps
        
        print(f"\n📈 统计结果:")
        print("-" * 40)
        print(f"总步骤数: {total_steps}")
        print(f"可能使用LLM: {likely_llm_steps} ({likely_llm_steps/total_steps*100:.1f}%)")
        print(f"可能硬编码: {likely_hardcoded_steps} ({likely_hardcoded_steps/total_steps*100:.1f}%)")
        
        # 建议
        if likely_hardcoded_steps > likely_llm_steps:
            print(f"\n💡 建议:")
            print("-" * 40)
            print("⚠️  检测到多个步骤可能使用硬编码结果，建议：")
            print("   1. 检查这些步骤是否应该调用LLM进行真实分析")
            print("   2. 确保用户偏好分析、驾驶情境分析等使用真实的LLM推理")
            print("   3. 避免返回固定的模拟数据")

async def main():
    """主测试函数"""
    print("🧪 LLM使用情况监控测试")
    print("检测各个节点是否真正使用了LLM\n")
    
    analyzer = LLMUsageAnalyzer()
    
    # 测试查询
    test_query = "我想去北京旅游3天，喜欢历史文化和传统美食，预算中等"
    
    # 执行测试
    await analyzer.test_with_llm_monitoring(test_query)

if __name__ == "__main__":
    asyncio.run(main())
