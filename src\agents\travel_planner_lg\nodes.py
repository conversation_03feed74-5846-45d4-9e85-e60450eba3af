"""
LangGraph 节点函数实现 (V2.0 - 集成NotificationService)

每个节点函数负责执行特定的任务步骤，并更新状态。
集成了实时通知服务，支持事件驱动的前端UI更新。
"""
import asyncio
import json
from typing import Dict, Any, List
from .state import TravelPlanState, CityPlanResult
from src.core.logger import get_logger
from src.services.notification_service import NotificationService
from src.agents.services import ReasoningService, UserProfileService, AmapService, AnalysisService

logger = get_logger("travel_planner_nodes")


async def analyze_core_intent(state: TravelPlanState) -> Dict[str, Any]:
    """分析核心意图节点
    
    解析用户的原始查询，提取目的地、时间、偏好等核心信息。
    """
    trace_id = state.get('trace_id', 'unknown')
    task_id = state.get('task_id', trace_id)  # 任务ID，用于Redis键
    notification_service = state.get('notification_service')
    
    step_name = "core_intent_analysis"
    step_title = "解析用户需求和画像"
    
    try:
        # 发送步骤开始事件
        if notification_service:
            await notification_service.notify_step_start(
                task_id, step_name, step_title, "正在分析您的旅行意图和需求..."
            )
        
        logger.info(f"[{trace_id}] 开始分析核心意图")
        
        # 使用服务进行分析
        analysis_service = AnalysisService()
        user_profile_service = UserProfileService()

        # 获取用户画像
        user_profile = await user_profile_service.get_user_profile(state['user_id'])

        # 调用分析服务
        core_intent = await analysis_service.analyze_core_intent(
            state['original_query'],
            state['user_id'],
            user_profile
        )

        destinations = core_intent.get("destinations", [])
        if not destinations:
            raise ValueError("未能从用户查询中提取有效目的地")

        # 生成旁白文本
        narration_text = f"我理解您想要规划一个{len(destinations)}个城市的旅行，主要目的地包括：{', '.join(destinations)}"
        
        # 构建结果
        result = {
            "destinations": destinations,
            "core_intent": core_intent,
            "current_narration_text": narration_text,
            "current_step": "核心意图分析完成",
            "progress_percentage": 20
        }
        
        # 发送步骤完成事件
        if notification_service:
            await notification_service.notify_step_end(
                task_id, step_name, "success", {
                    "content": f"识别出{len(destinations)}个目的地：{', '.join(destinations)}",
                    "destinations": destinations,
                    "duration_days": core_intent.get('days', core_intent.get('duration_days', 2))
                }
            )
        
        logger.info(f"[{trace_id}] 核心意图分析完成，目的地：{destinations}")
        return result
        
    except Exception as e:
        error_msg = f"核心意图分析失败: {str(e)}"
        logger.error(f"[{trace_id}] {error_msg}")
        
        # 发送错误事件
        if notification_service:
            await notification_service.notify_error(task_id, error_msg, step_name)
        
        # 返回错误状态
        return {
            "error": error_msg,
            "current_step": "核心意图分析失败",
            "progress_percentage": state.get("progress_percentage", 0)
        }


async def analyze_multi_city_strategy(state: TravelPlanState) -> Dict[str, Any]:
    """分析多城市策略节点
    
    如果识别出多个目的地，则生成宏观策略。
    在交互模式下，设置clarification_needed标志以暂停并等待用户确认。
    """
    trace_id = state.get('trace_id', 'unknown')
    task_id = state.get('task_id', trace_id)
    notification_service = state.get('notification_service')
    
    step_name = "multi_city_strategy"
    step_title = "多城市策略分析"
    
    try:
        destinations = state.get("destinations", [])
        
        # 发送步骤开始事件
        if notification_service:
            await notification_service.notify_step_start(
                task_id, step_name, step_title, 
                f"正在为{len(destinations)}个目的地制定旅行策略..."
            )
        
        logger.info(f"[{trace_id}] 开始分析多城市策略")
        
        if len(destinations) > 1:
            # 使用分析服务进行多城市策略分析
            analysis_service = AnalysisService()
            core_intent = state.get("core_intent", {})

            strategy = await analysis_service.analyze_multi_city_strategy(
                destinations=destinations,
                total_days=core_intent.get("days", 3),
                user_preferences=core_intent.get("interests"),
                transportation_mode="driving"  # 默认自驾
            )
            
            narration_text = strategy.get("narration", f"建议您按照 {' -> '.join(destinations)} 的顺序游览。")
            
            # 根据模式决定是否需要澄清
            needs_clarification = state.get("execution_mode") == "interactive"
            
            result = {
                "multi_city_strategy": strategy,
                "current_narration_text": narration_text,
                "clarification_needed": "multi_city_strategy" if needs_clarification else None,
                "current_step": "多城市策略分析完成",
                "progress_percentage": 30
            }
            
            # 发送步骤完成事件
            if notification_service:
                await notification_service.notify_step_end(
                    task_id, step_name, "success", {
                        "content": f"制定了{len(destinations)}城市策略：{narration_text}",
                        "strategy": strategy
                    }
                )
        else:
            # 单目的地，直接跳过
            result = {
                "current_step": "单目的地，跳过多城市策略",
                "progress_percentage": 30
            }
            
            # 发送步骤完成事件
            if notification_service:
                await notification_service.notify_step_end(
                    task_id, step_name, "success", {
                        "content": "单目的地旅行，无需多城市策略"
                    }
                )
        
        logger.info(f"[{trace_id}] 多城市策略分析完成")
        return result
        
    except Exception as e:
        error_msg = f"多城市策略分析失败: {str(e)}"
        logger.error(f"[{trace_id}] {error_msg}")
        
        # 发送错误事件
        if notification_service:
            await notification_service.notify_error(task_id, error_msg, step_name)
        
        return {
            "error": error_msg,
            "current_step": "多城市策略分析失败",
            "progress_percentage": state.get("progress_percentage", 20)
        }


async def analyze_driving_context(state: TravelPlanState) -> Dict[str, Any]:
    """分析驾驶情境节点
    
    分析用户的车辆信息和驾驶需求。
    """
    trace_id = state.get('trace_id', 'unknown')
    task_id = state.get('task_id', trace_id)
    notification_service = state.get('notification_service')
    
    step_name = "driving_context_analysis"
    step_title = "驾驶情境分析"
    
    try:
        # 发送步骤开始事件
        if notification_service:
            await notification_service.notify_step_start(
                task_id, step_name, step_title, "正在分析您的车辆信息和驾驶需求..."
            )
        
        logger.info(f"[{trace_id}] 开始分析驾驶情境")
        
        # 使用服务获取信息和分析
        user_profile_service = UserProfileService()
        reasoning_service = ReasoningService()

        # 获取用户画像（包含车辆信息）
        user_profile = await user_profile_service.get_user_profile(state['user_id'])
        vehicle_info = user_profile.get("vehicle_info", {}) if user_profile else {}

        # 真实的驾驶情境分析
        try:
            analysis_service = AnalysisService()
            driving_context = await analysis_service.analyze_driving_context(
                vehicle_info=vehicle_info,
                destinations=state.get("destinations", []),
                total_days=state.get("core_intent", {}).get("days", 3),
                user_preferences=state.get("user_preferences")
            )
        except Exception as e:
            logger.warning(f"[{trace_id}] 驾驶情境分析服务调用失败，使用默认配置: {str(e)}")
            # 备用方案：基于车辆信息的简单分析
            driving_context = {
                "strategy": "general_assistance",
                "planning_range_km": vehicle_info.get("range_km", 300),
                "range_buffer_factor": 0.8,
                "narration": f"基于您的{vehicle_info.get('model', '车辆')}，我会为您的自驾行程提供相关辅助信息。"
            }
        
        narration_text = driving_context.get("narration", "我会为您的自驾行程提供相关辅助信息。")
        
        result = {
            "user_vehicle_info": vehicle_info,
            "driving_strategy": driving_context.get("strategy", "general_assistance"),
            "planning_range_km": driving_context.get("planning_range_km"),
            "range_buffer_factor": driving_context.get("range_buffer_factor", 0.8),
            "current_narration_text": narration_text,
            "current_step": "驾驶情境分析完成",
            "progress_percentage": 40
        }
        
        # 发送步骤完成事件
        if notification_service:
            await notification_service.notify_step_end(
                task_id, step_name, "success", {
                    "content": narration_text,
                    "vehicle_model": vehicle_info.get("model"),
                    "driving_strategy": driving_context.get("strategy")
                }
            )
        
        logger.info(f"[{trace_id}] 驾驶情境分析完成，策略：{driving_context.get('strategy')}")
        return result
        
    except Exception as e:
        error_msg = f"驾驶情境分析失败: {str(e)}"
        logger.error(f"[{trace_id}] {error_msg}")
        
        # 发送错误事件
        if notification_service:
            await notification_service.notify_error(task_id, error_msg, step_name)
        
        return {
            "error": error_msg,
            "current_step": "驾驶情境分析失败",
            "progress_percentage": state.get("progress_percentage", 30)
        }


async def analyze_preferences(state: TravelPlanState) -> Dict[str, Any]:
    """分析用户偏好节点
    
    基于用户画像和记忆，分析用户的旅行偏好。
    """
    trace_id = state.get('trace_id', 'unknown')
    task_id = state.get('task_id', trace_id)
    notification_service = state.get('notification_service')
    
    step_name = "preference_analysis"
    step_title = "用户偏好分析"
    
    try:
        # 发送步骤开始事件
        if notification_service:
            await notification_service.notify_step_start(
                task_id, step_name, step_title, "正在分析您的个性化旅行偏好..."
            )
        
        logger.info(f"[{trace_id}] 开始分析用户偏好")
        
        # 使用服务进行分析
        analysis_service = AnalysisService()
        user_profile_service = UserProfileService()

        # 获取用户画像
        user_profile = await user_profile_service.get_user_profile(state['user_id'])

        # 真实的偏好分析
        try:
            preferences = await analysis_service.analyze_preferences(
                core_intent=state.get("core_intent", {}),
                user_profile=user_profile,
                user_memories=[]  # 可以从memory_service获取
            )
        except Exception as e:
            logger.warning(f"[{trace_id}] 偏好分析服务调用失败，使用基于查询的分析: {str(e)}")
            # 备用方案：基于用户查询的简单分析
            original_query = state.get("original_query", "")
            preferences = _analyze_preferences_from_query(original_query)
        
        narration_text = preferences.get("narration", "已完成您的个性化偏好分析。")

        result = {
            "attraction_preferences": preferences.get("attraction"),
            "food_preferences": preferences.get("food"),
            "accommodation_preferences": preferences.get("accommodation"),
            "current_narration_text": narration_text,
            "current_step": "用户偏好分析完成",
            "progress_percentage": 50
        }
        
        # 发送步骤完成事件
        if notification_service:
            await notification_service.notify_step_end(
                task_id, step_name, "success", {
                    "content": narration_text,
                    "attraction_types": result["attraction_preferences"]["types"],
                    "food_types": result["food_preferences"]["cuisine_types"],
                    "accommodation_type": result["accommodation_preferences"]["type"]
                }
            )
        
        logger.info(f"[{trace_id}] 用户偏好分析完成")
        return result
        
    except Exception as e:
        error_msg = f"用户偏好分析失败: {str(e)}"
        logger.error(f"[{trace_id}] {error_msg}")
        
        # 发送错误事件
        if notification_service:
            await notification_service.notify_error(task_id, error_msg, step_name)
        
        return {
            "error": error_msg,
            "current_step": "用户偏好分析失败",
            "progress_percentage": state.get("progress_percentage", 40)
        }


async def execute_planning_stage(state: TravelPlanState) -> Dict[str, Any]:
    """执行规划阶段节点
    
    调用高德地图等工具进行POI搜索、路线规划，并生成每日行程。
    """
    trace_id = state.get('trace_id', 'unknown')
    task_id = state.get('task_id', trace_id)
    notification_service = state.get('notification_service')
    
    step_name = "planning_execution"
    step_title = "执行旅行规划"
    
    try:
        # 发送步骤开始事件
        if notification_service:
            await notification_service.notify_step_start(
                task_id, step_name, step_title, "正在为您搜索景点、美食、酒店并规划路线..."
            )
        
        logger.info(f"[{trace_id}] 开始执行规划阶段")
        
        amap_service = AmapService()
        analysis_service = AnalysisService()
        
        destinations = state.get("destinations", [])
        city_plans: List[CityPlanResult] = []

        for city in destinations:
            # 1. 搜索POI - 使用现有的search_poi方法
            attractions = await amap_service.search_poi(
                keywords="景点",
                city=city,
                types="110000",  # 景点类型
                offset=10
            )

            foods = await amap_service.search_poi(
                keywords="餐厅",
                city=city,
                types="050000",  # 餐饮类型
                offset=10
            )

            accommodations = await amap_service.search_poi(
                keywords="酒店",
                city=city,
                types="100000",  # 住宿类型
                offset=5
            )

            # 2. 简化的POI处理
            all_pois = []
            for poi in attractions:
                poi["type"] = "attraction"
                all_pois.append(poi)
            for poi in foods:
                poi["type"] = "food"
                all_pois.append(poi)
            for poi in accommodations:
                poi["type"] = "accommodation"
                all_pois.append(poi)

            # 3. 智能的行程编排
            daily_itinerary = await _create_intelligent_itinerary(
                city=city,
                attractions=attractions,
                foods=foods,
                accommodations=accommodations,
                days=state.get("core_intent", {}).get("days", 2),
                amap_service=amap_service,
                trace_id=trace_id
            )

            city_plan = CityPlanResult(
                city=city,
                days=state.get("core_intent", {}).get("days", state.get("core_intent", {}).get("duration_days", 2)),
                planned_attractions=attractions,
                planned_restaurants=foods,
                planned_accommodation=accommodations[0] if accommodations else None
            )
            logger.info(f"[{trace_id}] 创建城市计划: {city}, 景点数: {len(attractions)}, 餐厅数: {len(foods)}, 酒店数: {len(accommodations)}")
            city_plans.append(city_plan)

        # 4. 真实的路线规划
        driving_routes = []
        user_location = state.get("user_location", "北京市")  # 默认位置或从用户画像获取

        for dest in destinations:
            try:
                # 使用高德地图API计算真实路线
                route_result = await amap_service.get_driving_route(
                    origin=user_location,
                    destination=dest
                )

                if route_result:
                    driving_routes.append({
                        "origin": user_location,
                        "destination": dest,
                        "distance": f"{route_result.get('distance', 0) / 1000:.1f}公里",
                        "duration": f"{route_result.get('duration', 0) / 60:.0f}分钟",
                        "route_detail": route_result
                    })
                else:
                    # API调用失败时的备用方案
                    driving_routes.append({
                        "origin": user_location,
                        "destination": dest,
                        "distance": "距离计算中...",
                        "duration": "时间计算中...",
                        "route_detail": None
                    })

            except Exception as e:
                logger.warning(f"[{trace_id}] 路线规划失败 {user_location} -> {dest}: {str(e)}")
                # 失败时使用估算值
                driving_routes.append({
                    "origin": user_location,
                    "destination": dest,
                    "distance": "距离待计算",
                    "duration": "时间待计算",
                    "route_detail": None
                })

        narration_text = f"已为您在{', '.join(destinations)}规划了详细行程，包含景点、美食和住宿。"

        result = {
            "city_plan_results": city_plans,  # CityPlanResult是TypedDict，直接使用
            "driving_routes": driving_routes,
            "current_narration_text": narration_text,
            "current_step": "规划阶段执行完成",
            "progress_percentage": 80
        }
        
        # 发送步骤完成事件
        if notification_service:
            await notification_service.notify_step_end(
                task_id, step_name, "success", {
                    "content": narration_text,
                    "total_pois": sum(len(cp.get("planned_attractions", [])) + len(cp.get("planned_restaurants", [])) for cp in city_plans)
                }
            )
        
        logger.info(f"[{trace_id}] 规划阶段执行完成")
        return result
        
    except Exception as e:
        error_msg = f"规划阶段执行失败: {str(e)}"
        logger.error(f"[{trace_id}] {error_msg}")
        
        # 发送错误事件
        if notification_service:
            await notification_service.notify_error(task_id, error_msg, step_name)
        
        return {
            "error": error_msg,
            "current_step": "规划阶段执行失败",
            "progress_percentage": state.get("progress_percentage", 50)
        }


async def finalize_result(state: TravelPlanState) -> Dict[str, Any]:
    """最终化结果节点
    
    整合所有信息，生成最终的结构化行程。
    """
    trace_id = state.get('trace_id', 'unknown')
    task_id = state.get('task_id', trace_id)
    notification_service = state.get('notification_service')
    
    step_name = "result_finalization"
    step_title = "完成旅行规划"
    
    try:
        # 发送步骤开始事件
        if notification_service:
            await notification_service.notify_step_start(
                task_id, step_name, step_title, "正在为您生成最终的旅行方案..."
            )
        
        logger.info(f"[{trace_id}] 开始最终化结果")
        
        # 生成最终行程
        city_plans = state.get("city_plan_results", [])
        destinations = state.get("destinations", [])

        # 构建简化的最终行程
        final_itinerary = {
            "title": f"{', '.join(destinations)}旅行计划",
            "destinations": destinations,
            "total_days": state.get("core_intent", {}).get("days", state.get("core_intent", {}).get("duration_days", 2)),
            "city_plans": city_plans,
            "driving_routes": state.get("driving_routes", []),
            "summary": f"为您规划了{len(destinations)}个城市的精彩行程"
        }

        narration_text = final_itinerary.get("summary", "您的专属旅行计划已生成！")
        
        result = {
            "final_itinerary": final_itinerary,
            "current_narration_text": narration_text,
            "current_step": "旅行规划完成",
            "progress_percentage": 100
        }
        
        # 发送最终结果事件
        if notification_service:
            await notification_service.notify_final_result(
                task_id, final_itinerary
            )
        
        logger.info(f"[{trace_id}] 旅行规划完成")
        return result
        
    except Exception as e:
        error_msg = f"结果最终化失败: {str(e)}"
        logger.error(f"[{trace_id}] {error_msg}")
        
        # 发送错误事件
        if notification_service:
            await notification_service.notify_error(task_id, error_msg, step_name)
        
        return {
            "error": error_msg,
            "current_step": "结果最终化失败",
            "progress_percentage": state.get("progress_percentage", 80)
        }


def wait_for_user_input(state: TravelPlanState) -> Dict[str, Any]:
    """等待用户输入节点
    
    在交互模式下暂停执行，等待用户确认。
    """
    trace_id = state.get('trace_id', 'unknown')
    logger.info(f"[{trace_id}] 等待用户输入")
    
    # 这个节点主要是标记状态，实际的等待逻辑在图的执行层处理
    return {
        "current_step": "等待用户确认",
        "progress_percentage": state.get("progress_percentage", 50)
    }


def route_after_analysis(state: TravelPlanState) -> str:
    """分析后的路由函数
    
    决定下一步走向：是否需要等待用户输入。
    """
    if state.get("clarification_needed"):
        return "wait_for_user_input"
    else:
        return "continue_analysis"


def decide_planning_or_end(state: TravelPlanState) -> str:
    """决定是继续规划还是结束

    在分析的最后阶段决定是继续规划还是结束。
    """
    # 检查是否有错误
    if state.get("error"):
        return "__end__"

    # 在自动模式下，总是继续
    if state.get("execution_mode") == "automatic":
        return "execute_planning_stage"

    # 在交互模式下，根据用户反馈决定
    if state.get("user_feedback") == "proceed":
        return "execute_planning_stage"
    else:
        return "__end__"


async def _create_intelligent_itinerary(
    city: str,
    attractions: list,
    foods: list,
    accommodations: list,
    days: int,
    amap_service,
    trace_id: str
) -> dict:
    """
    创建智能的行程编排

    Args:
        city: 城市名称
        attractions: 景点列表
        foods: 餐厅列表
        accommodations: 酒店列表
        days: 天数
        amap_service: 高德地图服务
        trace_id: 追踪ID

    Returns:
        智能编排的行程
    """
    logger.info(f"[{trace_id}] 开始为{city}创建{days}天智能行程")

    try:
        # 1. 对POI进行评分和排序
        scored_attractions = await _score_and_sort_pois(attractions, "attraction", amap_service)
        scored_foods = await _score_and_sort_pois(foods, "food", amap_service)

        # 2. 选择最佳住宿（通常选择评分最高且位置便利的）
        best_accommodation = None
        if accommodations:
            scored_accommodations = await _score_and_sort_pois(accommodations, "accommodation", amap_service)
            best_accommodation = scored_accommodations[0] if scored_accommodations else accommodations[0]

        # 3. 按天分配POI
        daily_itinerary = {}
        attractions_per_day = max(2, len(scored_attractions) // days)  # 每天至少2个景点
        foods_per_day = max(2, len(scored_foods) // days)  # 每天至少2个餐厅

        for day in range(1, days + 1):
            start_idx_attr = (day - 1) * attractions_per_day
            end_idx_attr = min(day * attractions_per_day, len(scored_attractions))

            start_idx_food = (day - 1) * foods_per_day
            end_idx_food = min(day * foods_per_day, len(scored_foods))

            day_attractions = scored_attractions[start_idx_attr:end_idx_attr]
            day_foods = scored_foods[start_idx_food:end_idx_food]

            # 4. 基于地理位置优化当天行程顺序
            optimized_attractions = await _optimize_daily_route(day_attractions, amap_service, trace_id)
            optimized_foods = await _optimize_daily_route(day_foods, amap_service, trace_id)

            daily_itinerary[f"day_{day}"] = {
                "attractions": optimized_attractions,
                "foods": optimized_foods,
                "accommodation": best_accommodation if day == 1 else None,  # 只在第一天显示住宿
                "estimated_travel_time": await _calculate_daily_travel_time(
                    optimized_attractions + optimized_foods, amap_service
                )
            }

        logger.info(f"[{trace_id}] {city}智能行程创建完成，共{days}天")
        return daily_itinerary

    except Exception as e:
        logger.error(f"[{trace_id}] 智能行程创建失败: {str(e)}")
        # 返回简化版本作为备用
        return {
            f"day_1": {
                "attractions": attractions[:3],
                "foods": foods[:2],
                "accommodation": accommodations[0] if accommodations else None,
                "estimated_travel_time": "时间计算失败"
            }
        }


async def _score_and_sort_pois(pois: list, poi_type: str, amap_service) -> list:
    """
    对POI进行评分和排序

    Args:
        pois: POI列表
        poi_type: POI类型 (attraction, food, accommodation)
        amap_service: 高德地图服务

    Returns:
        排序后的POI列表
    """
    if not pois:
        return []

    try:
        scored_pois = []
        for poi in pois:
            score = 0

            # 基础评分：评分和评论数
            rating = float(poi.get('rating', 0))
            review_count = int(poi.get('review_count', 0))

            score += rating * 20  # 评分权重
            score += min(review_count / 100, 10)  # 评论数权重，最多10分

            # 类型特定评分
            if poi_type == "attraction":
                # 景点：考虑知名度和类型
                if "5A" in poi.get('name', '') or "4A" in poi.get('name', ''):
                    score += 15
                if any(keyword in poi.get('name', '') for keyword in ['博物馆', '公园', '寺庙', '古迹']):
                    score += 10

            elif poi_type == "food":
                # 餐厅：考虑人均消费和特色
                avg_price = poi.get('avg_price', 0)
                if 50 <= avg_price <= 200:  # 合理价格区间
                    score += 10
                if any(keyword in poi.get('name', '') for keyword in ['特色', '老字号', '招牌']):
                    score += 5

            elif poi_type == "accommodation":
                # 住宿：考虑星级和位置
                if "五星" in poi.get('name', '') or "4星" in poi.get('name', ''):
                    score += 15
                if "市中心" in poi.get('address', '') or "商圈" in poi.get('address', ''):
                    score += 10

            poi['_score'] = score
            scored_pois.append(poi)

        # 按评分排序
        scored_pois.sort(key=lambda x: x.get('_score', 0), reverse=True)
        return scored_pois

    except Exception as e:
        logger.warning(f"POI评分失败: {str(e)}")
        return pois  # 返回原始列表


async def _optimize_daily_route(pois: list, amap_service, trace_id: str) -> list:
    """
    优化当天的POI访问顺序

    Args:
        pois: 当天的POI列表
        amap_service: 高德地图服务
        trace_id: 追踪ID

    Returns:
        优化后的POI顺序
    """
    if len(pois) <= 1:
        return pois

    try:
        # 简化的路线优化：基于地理位置聚类
        # 这里可以实现更复杂的TSP算法，但为了性能考虑使用简化版本

        # 按经纬度排序，实现简单的地理聚类
        pois_with_location = []
        for poi in pois:
            location = poi.get('location', '').split(',')
            if len(location) == 2:
                try:
                    lng, lat = float(location[0]), float(location[1])
                    poi['_lng'] = lng
                    poi['_lat'] = lat
                    pois_with_location.append(poi)
                except ValueError:
                    pois_with_location.append(poi)  # 保留无坐标的POI
            else:
                pois_with_location.append(poi)

        # 按经度排序，然后按纬度排序，实现简单的路线优化
        pois_with_location.sort(key=lambda x: (x.get('_lng', 0), x.get('_lat', 0)))

        logger.info(f"[{trace_id}] 路线优化完成，POI数量: {len(pois_with_location)}")
        return pois_with_location

    except Exception as e:
        logger.warning(f"[{trace_id}] 路线优化失败: {str(e)}")
        return pois  # 返回原始顺序


async def _calculate_daily_travel_time(pois: list, amap_service) -> str:
    """
    计算当天的预估旅行时间

    Args:
        pois: POI列表
        amap_service: 高德地图服务

    Returns:
        预估旅行时间字符串
    """
    if len(pois) <= 1:
        return "无需交通时间"

    try:
        total_time = 0
        successful_calculations = 0

        for i in range(len(pois) - 1):
            current_poi = pois[i]
            next_poi = pois[i + 1]

            current_location = current_poi.get('location', '')
            next_location = next_poi.get('location', '')

            if current_location and next_location:
                try:
                    # 使用高德地图API计算两点间距离
                    route_result = await amap_service.get_driving_route(
                        origin=current_location,
                        destination=next_location
                    )

                    if route_result:
                        duration = route_result.get("duration", 0)
                        total_time += duration
                        successful_calculations += 1

                except Exception:
                    # 单次计算失败，跳过
                    continue

        if successful_calculations > 0:
            avg_time_per_segment = total_time / successful_calculations
            estimated_total = avg_time_per_segment * (len(pois) - 1)
            return f"约{estimated_total / 60:.0f}分钟交通时间"
        else:
            return "交通时间计算中..."

    except Exception as e:
        logger.warning(f"旅行时间计算失败: {str(e)}")
        return "时间待计算"


def _analyze_preferences_from_query(query: str) -> dict:
    """
    基于用户查询的简单偏好分析（备用方案）

    Args:
        query: 用户原始查询

    Returns:
        偏好分析结果
    """
    preferences = {
        "attraction": {
            "types": [],
            "style": "常规游"
        },
        "food": {
            "cuisine_types": [],
            "budget_level": "中等"
        },
        "accommodation": {
            "type": "经济型酒店",
            "location_preference": "市中心"
        },
        "narration": "已基于您的需求完成偏好分析。"
    }

    query_lower = query.lower()

    # 景点偏好分析
    if any(keyword in query_lower for keyword in ['历史', '文化', '古迹', '博物馆', '寺庙']):
        preferences["attraction"]["types"].append("历史文化")
    if any(keyword in query_lower for keyword in ['自然', '风景', '山', '海', '湖', '公园']):
        preferences["attraction"]["types"].append("自然风光")
    if any(keyword in query_lower for keyword in ['现代', '都市', '购物', '商圈']):
        preferences["attraction"]["types"].append("现代都市")

    # 美食偏好分析
    if any(keyword in query_lower for keyword in ['美食', '小吃', '特色', '当地']):
        preferences["food"]["cuisine_types"].append("本地特色")
    if any(keyword in query_lower for keyword in ['海鲜', '海边', '沿海']):
        preferences["food"]["cuisine_types"].append("海鲜")

    # 预算分析
    if any(keyword in query_lower for keyword in ['经济', '便宜', '省钱', '预算']):
        preferences["food"]["budget_level"] = "经济"
        preferences["accommodation"]["type"] = "经济型酒店"
    elif any(keyword in query_lower for keyword in ['豪华', '高端', '奢华']):
        preferences["food"]["budget_level"] = "高端"
        preferences["accommodation"]["type"] = "豪华酒店"

    # 如果没有识别到特定偏好，使用默认值
    if not preferences["attraction"]["types"]:
        preferences["attraction"]["types"] = ["综合景点"]
    if not preferences["food"]["cuisine_types"]:
        preferences["food"]["cuisine_types"] = ["本地特色"]

    return preferences
