"""
测试修复后的立即规划功能
验证硬编码问题是否已解决
"""

import asyncio
import json
import httpx
import time
from typing import List, Dict, Any

# API配置
BASE_URL = "http://localhost:8000"
HEADERS = {"Content-Type": "application/json"}

class PlanningTester:
    def __init__(self):
        self.base_url = BASE_URL
        self.headers = HEADERS
    
    async def test_fixed_immediate_planning(self, query: str, user_id: str = "test_user_fixed"):
        """
        测试修复后的立即规划接口
        
        验证：
        1. 路线规划不再使用硬编码的"约100公里"、"约2小时"
        2. 行程编排使用智能算法而非简单的前N个POI
        3. 返回真实的距离和时间计算
        """
        print("=" * 60)
        print("🔧 测试修复后的立即规划功能")
        print("=" * 60)
        
        # 使用V2 SSE接口，执行完整规划
        url = f"{self.base_url}/api/travel/v2/plan/stream"
        
        request_data = {
            "query": query,
            "user_id": user_id
        }
        
        print(f"📝 请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
        print(f"🌐 请求URL: {url}")
        print("\n📡 SSE事件流 (修复后的规划):")
        print("-" * 40)
        
        events = []
        hardcoded_issues = []
        
        try:
            async with httpx.AsyncClient(timeout=120.0) as client:
                async with client.stream("POST", url, json=request_data, headers=self.headers) as response:
                    if response.status_code != 200:
                        print(f"❌ HTTP错误: {response.status_code}")
                        return events, hardcoded_issues
                    
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data_str = line[6:]  # 移除 "data: " 前缀
                            
                            if data_str.strip() == "[DONE]":
                                print("✅ 流式传输完成")
                                break
                            
                            try:
                                event_data = json.loads(data_str)
                                events.append(event_data)
                                
                                # 显示事件信息
                                event_type = event_data.get("event_type", "unknown")
                                step_name = event_data.get("step_name", "")
                                progress = event_data.get("progress_percentage", 0)
                                
                                print(f"📨 [{event_type}] {step_name} - 进度: {progress}%")
                                
                                # 检查硬编码问题
                                payload = event_data.get("payload", {})
                                self._check_for_hardcoded_issues(payload, hardcoded_issues)
                                
                                # 显示重要数据
                                if event_type == "step_end" and "driving_routes" in payload:
                                    print("🚗 路线规划结果:")
                                    for route in payload["driving_routes"]:
                                        origin = route.get("origin", "N/A")
                                        dest = route.get("destination", "N/A")
                                        distance = route.get("distance", "N/A")
                                        duration = route.get("duration", "N/A")
                                        print(f"   {origin} -> {dest}: {distance}, {duration}")
                                
                                if event_type == "step_end" and "city_plan_results" in payload:
                                    print("🏙️ 城市规划结果:")
                                    for city_plan in payload["city_plan_results"]:
                                        city = city_plan.get("city", "N/A")
                                        attractions = len(city_plan.get("planned_attractions", []))
                                        foods = len(city_plan.get("planned_restaurants", []))
                                        print(f"   {city}: {attractions}个景点, {foods}个餐厅")
                                
                                if event_type == "final_result":
                                    print("🎯 最终结果:")
                                    final_data = payload.get("final_itinerary", {})
                                    if final_data:
                                        print(f"   {json.dumps(final_data, ensure_ascii=False, indent=2)[:500]}...")
                                        
                            except json.JSONDecodeError as e:
                                print(f"❌ JSON解析错误: {e}")
                                print(f"原始数据: {line}")
        
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
        
        print(f"\n📊 总共收到 {len(events)} 个事件")
        
        # 分析硬编码问题
        if hardcoded_issues:
            print(f"\n⚠️ 发现 {len(hardcoded_issues)} 个硬编码问题:")
            for issue in hardcoded_issues:
                print(f"   - {issue}")
        else:
            print("\n✅ 未发现硬编码问题")
        
        return events, hardcoded_issues
    
    def _check_for_hardcoded_issues(self, payload: dict, issues: list):
        """检查payload中的硬编码问题"""
        
        # 检查路线规划中的硬编码
        if "driving_routes" in payload:
            for route in payload["driving_routes"]:
                distance = route.get("distance", "")
                duration = route.get("duration", "")
                
                # 检查是否包含硬编码的文本
                if "约100公里" in distance:
                    issues.append("路线距离使用硬编码: '约100公里'")
                if "约2小时" in duration:
                    issues.append("路线时间使用硬编码: '约2小时'")
                if "用户当前位置" in route.get("origin", ""):
                    issues.append("起点使用硬编码: '用户当前位置'")
                if "距离计算中" in distance or "时间计算中" in duration:
                    issues.append("距离或时间计算失败，使用占位符")
        
        # 检查行程编排中的问题
        if "city_plan_results" in payload:
            for city_plan in payload["city_plan_results"]:
                attractions = city_plan.get("planned_attractions", [])
                foods = city_plan.get("planned_restaurants", [])
                
                # 检查是否只是简单取前N个
                if len(attractions) == 3 and len(foods) == 2:
                    # 这可能是硬编码的简单取值，需要进一步检查
                    pass
        
        # 检查其他可能的硬编码文本
        payload_str = json.dumps(payload, ensure_ascii=False)
        hardcoded_patterns = [
            "示例", "测试", "模拟", "硬编码", "TODO", "待实现"
        ]
        
        for pattern in hardcoded_patterns:
            if pattern in payload_str:
                issues.append(f"发现可能的硬编码文本: '{pattern}'")

async def main():
    """主测试函数"""
    print("🧪 立即规划硬编码修复验证测试")
    print("验证路线规划和行程编排是否使用真实API数据\n")
    
    tester = PlanningTester()
    
    # 测试查询
    test_query = "我想去厦门旅游2天，喜欢海边风景和当地美食"
    
    # 测试修复后的立即规划
    events, issues = await tester.test_fixed_immediate_planning(test_query)
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    print(f"✅ 总事件数: {len(events)}")
    print(f"{'❌' if issues else '✅'} 硬编码问题: {len(issues)}个")
    
    if issues:
        print("\n需要进一步修复的问题:")
        for i, issue in enumerate(issues, 1):
            print(f"{i}. {issue}")
    else:
        print("\n🎉 所有硬编码问题已修复！")

if __name__ == "__main__":
    asyncio.run(main())
