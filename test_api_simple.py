"""
简单的API测试
验证服务是否正常运行
"""

import asyncio
import httpx
import json

async def test_health():
    """测试健康检查接口"""
    print("🔍 测试健康检查接口")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8000/health")
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.text}")
            return response.status_code == 200
    except Exception as e:
        print(f"❌ 健康检查失败: {str(e)}")
        return False

async def test_simple_planning():
    """测试简单的规划接口"""
    print("\n🔍 测试简单规划接口")
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            # 测试单城市
            data = {
                "query": "我想去北京旅游3天",
                "user_id": "test_simple"
            }
            
            print(f"发送请求: {json.dumps(data, ensure_ascii=False)}")
            
            response = await client.post(
                "http://localhost:8000/api/travel/v2/plan/stream",
                json=data,
                headers={"Content-Type": "application/json"}
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 请求成功，开始读取SSE流...")
                
                count = 0
                async for line in response.aiter_lines():
                    count += 1
                    print(f"[{count}] {line}")
                    
                    if count > 10:  # 只读取前10行
                        print("... (截断)")
                        break
                        
                return True
            else:
                print(f"❌ 请求失败: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 规划测试失败: {str(e)}")
        return False

async def main():
    print("🧪 简单API测试")
    print("=" * 40)
    
    # 测试健康检查
    health_ok = await test_health()
    
    if health_ok:
        # 测试规划接口
        planning_ok = await test_simple_planning()
        
        if planning_ok:
            print("\n✅ API测试通过")
        else:
            print("\n❌ 规划接口测试失败")
    else:
        print("\n❌ 服务未正常运行")

if __name__ == "__main__":
    asyncio.run(main())
