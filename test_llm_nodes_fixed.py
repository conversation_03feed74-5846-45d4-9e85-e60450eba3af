"""
测试修复后的LLM节点
验证是否真正调用了LLM而不是硬编码
"""

import asyncio
import time
from src.agents.travel_planner_lg.nodes import (
    analyze_multi_city_strategy,
    analyze_driving_context, 
    analyze_preferences
)
from src.agents.travel_planner_lg.state import TravelPlanState

async def test_node_llm_usage():
    """测试各个节点是否真正使用LLM"""
    print("🧪 测试修复后的LLM节点")
    print("=" * 50)
    
    # 创建测试状态
    test_state = {
        "trace_id": "test_llm_001",
        "task_id": "test_llm_001", 
        "user_id": "test_user",
        "original_query": "我想去北京旅游3天，喜欢历史文化和传统美食，预算中等",
        "destinations": ["北京"],
        "core_intent": {
            "destinations": ["北京"],
            "days": 3,
            "interests": ["历史文化", "传统美食"],
            "budget": "中等"
        },
        "execution_mode": "automatic",
        "notification_service": None
    }
    
    results = {}
    
    # 测试1: 多城市策略分析
    print("\n1️⃣ 测试多城市策略分析节点")
    print("-" * 30)
    start_time = time.time()
    try:
        result1 = await analyze_multi_city_strategy(test_state)
        execution_time = time.time() - start_time
        
        print(f"⏱️  执行时间: {execution_time:.2f}秒")
        print(f"📋 结果: {result1.get('current_step', 'N/A')}")
        
        # 检查是否是硬编码
        if execution_time < 0.5:
            print("⚡ 执行过快，可能是硬编码")
        else:
            print("🤖 执行时间正常，可能使用了LLM")
            
        results["multi_city_strategy"] = {
            "time": execution_time,
            "likely_llm": execution_time >= 0.5,
            "result": result1
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        results["multi_city_strategy"] = {"error": str(e)}
    
    # 测试2: 驾驶情境分析
    print("\n2️⃣ 测试驾驶情境分析节点")
    print("-" * 30)
    start_time = time.time()
    try:
        result2 = await analyze_driving_context(test_state)
        execution_time = time.time() - start_time
        
        print(f"⏱️  执行时间: {execution_time:.2f}秒")
        print(f"📋 结果: {result2.get('current_narration_text', 'N/A')}")
        
        # 检查是否是硬编码
        if execution_time < 0.5:
            print("⚡ 执行过快，可能是硬编码")
        else:
            print("🤖 执行时间正常，可能使用了LLM")
            
        results["driving_context"] = {
            "time": execution_time,
            "likely_llm": execution_time >= 0.5,
            "result": result2
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        results["driving_context"] = {"error": str(e)}
    
    # 测试3: 用户偏好分析
    print("\n3️⃣ 测试用户偏好分析节点")
    print("-" * 30)
    start_time = time.time()
    try:
        result3 = await analyze_preferences(test_state)
        execution_time = time.time() - start_time
        
        print(f"⏱️  执行时间: {execution_time:.2f}秒")
        print(f"📋 结果: {result3.get('current_narration_text', 'N/A')}")
        
        # 检查偏好是否基于查询内容
        attraction_prefs = result3.get('attraction_preferences', {})
        food_prefs = result3.get('food_preferences', {})
        
        print(f"🎯 景点偏好: {attraction_prefs.get('types', [])}")
        print(f"🍽️ 美食偏好: {food_prefs.get('cuisine_types', [])}")
        
        # 检查是否是硬编码
        if execution_time < 0.5:
            print("⚡ 执行过快，可能是硬编码")
        else:
            print("🤖 执行时间正常，可能使用了LLM")
            
        # 检查偏好是否合理
        query_keywords = ["历史文化", "传统美食"]
        prefs_match_query = any(
            keyword in str(attraction_prefs) or keyword in str(food_prefs) 
            for keyword in query_keywords
        )
        
        if prefs_match_query:
            print("✅ 偏好分析与查询内容匹配")
        else:
            print("⚠️ 偏好分析可能未考虑查询内容")
            
        results["preferences"] = {
            "time": execution_time,
            "likely_llm": execution_time >= 0.5,
            "prefs_match_query": prefs_match_query,
            "result": result3
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        results["preferences"] = {"error": str(e)}
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试总结")
    print("=" * 50)
    
    total_nodes = len(results)
    llm_nodes = sum(1 for r in results.values() if r.get("likely_llm", False))
    error_nodes = sum(1 for r in results.values() if "error" in r)
    
    print(f"总节点数: {total_nodes}")
    print(f"可能使用LLM: {llm_nodes}")
    print(f"可能硬编码: {total_nodes - llm_nodes - error_nodes}")
    print(f"错误节点: {error_nodes}")
    
    if llm_nodes == total_nodes - error_nodes:
        print("\n🎉 所有节点都可能使用了LLM！")
    elif llm_nodes > 0:
        print(f"\n⚠️ 部分节点({llm_nodes}/{total_nodes})可能使用了LLM")
    else:
        print("\n❌ 所有节点都可能是硬编码")
    
    return results

async def main():
    """主函数"""
    print("🔧 LLM节点修复验证测试")
    print("验证节点是否真正调用LLM而不是返回硬编码结果\n")
    
    results = await test_node_llm_usage()
    
    # 详细分析
    print("\n📋 详细分析:")
    for node_name, result in results.items():
        if "error" in result:
            print(f"❌ {node_name}: 测试失败 - {result['error']}")
        else:
            time_taken = result.get("time", 0)
            likely_llm = result.get("likely_llm", False)
            status = "🤖 可能使用LLM" if likely_llm else "⚡ 可能硬编码"
            print(f"{status} {node_name}: {time_taken:.2f}秒")

if __name__ == "__main__":
    asyncio.run(main())
