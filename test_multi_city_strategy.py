"""
测试多城市策略功能
验证多目的地时是否真正使用LLM进行策略分析
"""

import asyncio
import json
import time
import httpx
from typing import List, Dict, Any

# API配置
BASE_URL = "http://localhost:8000"
HEADERS = {"Content-Type": "application/json"}

class MultiCityTester:
    def __init__(self):
        self.base_url = BASE_URL
        self.headers = HEADERS
    
    async def test_multi_city_planning(self, query: str, user_id: str = "test_multi_city"):
        """
        测试多城市规划功能
        
        Args:
            query: 包含多个目的地的查询
            user_id: 测试用户ID
        """
        print("🏙️ 测试多城市策略规划")
        print("=" * 60)
        print(f"📝 查询: {query}")
        print(f"👤 用户: {user_id}")
        
        # 使用V2 SSE接口
        url = f"{self.base_url}/api/travel/v2/plan/stream"
        
        request_data = {
            "query": query,
            "user_id": user_id
        }
        
        print(f"\n🌐 请求URL: {url}")
        print(f"📋 请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
        print("\n📡 SSE事件流:")
        print("-" * 40)
        
        events = []
        multi_city_analysis = None
        
        try:
            async with httpx.AsyncClient(timeout=120.0) as client:
                async with client.stream("POST", url, json=request_data, headers=self.headers) as response:
                    if response.status_code != 200:
                        print(f"❌ HTTP错误: {response.status_code}")
                        return events, None
                    
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data_str = line[6:]  # 移除 "data: " 前缀
                            
                            if data_str.strip() == "[DONE]":
                                print("✅ 流式传输完成")
                                break
                            
                            try:
                                event_data = json.loads(data_str)
                                events.append(event_data)
                                
                                # 显示事件信息
                                event_type = event_data.get("event_type", "unknown")
                                step_name = event_data.get("step_name", "")
                                progress = event_data.get("progress_percentage", 0)
                                
                                print(f"📨 [{event_type}] {step_name} - 进度: {progress}%")
                                
                                # 特别关注多城市策略分析
                                if step_name == "multi_city_strategy":
                                    payload = event_data.get("payload", {})
                                    
                                    if event_type == "step_start":
                                        start_time = event_data.get("timestamp")
                                        print(f"   🚀 多城市策略分析开始: {start_time}")
                                    
                                    elif event_type == "step_end":
                                        end_time = event_data.get("timestamp")
                                        content = payload.get("content", "")
                                        strategy = payload.get("strategy", {})
                                        
                                        print(f"   ✅ 多城市策略分析完成: {end_time}")
                                        print(f"   📋 分析结果: {content}")
                                        
                                        if strategy:
                                            print(f"   🗺️ 策略详情:")
                                            print(f"      策略类型: {strategy.get('strategy_type', 'N/A')}")
                                            print(f"      推荐路线: {strategy.get('recommended_route', 'N/A')}")
                                            print(f"      总距离: {strategy.get('total_distance', 'N/A')}")
                                            print(f"      预计时间: {strategy.get('total_duration', 'N/A')}")
                                        
                                        multi_city_analysis = {
                                            "content": content,
                                            "strategy": strategy,
                                            "timestamp": end_time
                                        }
                                
                                # 显示其他重要步骤
                                if event_type == "step_end":
                                    payload = event_data.get("payload", {})
                                    if "destinations" in payload:
                                        destinations = payload["destinations"]
                                        print(f"   🎯 识别目的地: {destinations}")
                                        
                            except json.JSONDecodeError as e:
                                print(f"❌ JSON解析错误: {e}")
                                print(f"原始数据: {line}")
        
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
        
        print(f"\n📊 总共收到 {len(events)} 个事件")
        return events, multi_city_analysis
    
    def analyze_multi_city_performance(self, events: List[Dict], multi_city_analysis: Dict):
        """分析多城市策略的性能和质量"""
        print("\n" + "=" * 60)
        print("📊 多城市策略分析报告")
        print("=" * 60)
        
        # 查找多城市策略相关事件
        multi_city_events = [
            event for event in events 
            if event.get("step_name") == "multi_city_strategy"
        ]
        
        if not multi_city_events:
            print("❌ 未找到多城市策略分析事件")
            return
        
        # 计算执行时间
        start_event = None
        end_event = None
        
        for event in multi_city_events:
            if event.get("event_type") == "step_start":
                start_event = event
            elif event.get("event_type") == "step_end":
                end_event = event
        
        if start_event and end_event:
            # 这里简化时间计算，实际应该解析timestamp
            execution_time = len(multi_city_events) * 0.5  # 估算
            print(f"⏱️ 执行时间: 约{execution_time:.1f}秒")
            
            if execution_time < 0.5:
                print("⚡ 执行过快，可能是硬编码")
            else:
                print("🤖 执行时间正常，可能使用了LLM")
        
        # 分析策略质量
        if multi_city_analysis:
            strategy = multi_city_analysis.get("strategy", {})
            content = multi_city_analysis.get("content", "")
            
            print(f"\n📋 策略分析质量:")
            print(f"   内容长度: {len(content)}字符")
            
            # 检查是否包含硬编码文本
            hardcoded_patterns = [
                "单目的地旅行，无需多城市策略",
                "简单", "默认", "硬编码"
            ]
            
            has_hardcoded = any(pattern in content for pattern in hardcoded_patterns)
            
            if has_hardcoded:
                print("   ⚠️ 可能包含硬编码文本")
            else:
                print("   ✅ 内容看起来是真实分析")
            
            # 检查策略完整性
            if strategy:
                required_fields = ["strategy_type", "recommended_route"]
                missing_fields = [field for field in required_fields if not strategy.get(field)]
                
                if missing_fields:
                    print(f"   ⚠️ 缺少策略字段: {missing_fields}")
                else:
                    print("   ✅ 策略信息完整")
            else:
                print("   ❌ 未返回策略详情")
        else:
            print("❌ 未获取到多城市分析结果")

async def main():
    """主测试函数"""
    print("🧪 多城市策略功能测试")
    print("验证多目的地规划是否使用真实LLM分析\n")
    
    tester = MultiCityTester()
    
    # 测试用例1: 两个城市
    print("🔍 测试用例1: 两个城市")
    query1 = "我想去北京和上海旅游5天，先去北京2天看历史文化，再去上海3天体验现代都市"
    events1, analysis1 = await tester.test_multi_city_planning(query1, "test_multi_1")
    tester.analyze_multi_city_performance(events1, analysis1)
    
    # 等待一下避免请求冲突
    await asyncio.sleep(3)
    
    # 测试用例2: 三个城市
    print("\n" + "="*80)
    print("🔍 测试用例2: 三个城市")
    query2 = "我想去北京、西安、成都三个城市旅游7天，喜欢历史文化和美食"
    events2, analysis2 = await tester.test_multi_city_planning(query2, "test_multi_2")
    tester.analyze_multi_city_performance(events2, analysis2)
    
    # 总结
    print("\n" + "="*80)
    print("📊 多城市测试总结")
    print("="*80)
    
    test_cases = [
        ("两城市测试", events1, analysis1),
        ("三城市测试", events2, analysis2)
    ]
    
    for name, events, analysis in test_cases:
        print(f"\n{name}:")
        if analysis:
            strategy = analysis.get("strategy", {})
            if strategy:
                print(f"  ✅ 生成了策略: {strategy.get('strategy_type', 'N/A')}")
            else:
                print(f"  ⚠️ 未生成详细策略")
        else:
            print(f"  ❌ 未获取到分析结果")

if __name__ == "__main__":
    asyncio.run(main())
