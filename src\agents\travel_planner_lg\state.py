"""
LangGraph State 定义

定义旅行规划 Agent 的状态对象，用于在图的节点之间传递数据。
"""
from typing import TypedDict, List, Dict, Any, Optional, Literal
from src.models.travel_planner import UserProfile, TravelItinerary


class CityPlanResult(TypedDict):
    """单个城市的规划结果"""
    city: str
    days: int
    planned_attractions: Optional[List[Dict[str, Any]]]
    planned_restaurants: Optional[List[Dict[str, Any]]]
    planned_accommodation: Optional[Dict[str, Any]]


class TravelPlanState(TypedDict):
    """旅行规划状态对象
    
    这个状态对象在整个 LangGraph 的节点之间传递，包含了任务执行的所有信息。
    """
    # 基础信息
    trace_id: str
    task_id: str  # 任务ID，用于Redis推送频道
    user_id: str
    original_query: str
    
    # 推送服务（V2推送架构核心组件）
    notification_service: Optional[Any]  # NotificationService实例
    
    # 运行模式标志，在图的入口设定
    execution_mode: Literal["interactive", "automatic"]
    
    # 目的地信息（支持多目的地）
    destinations: List[str]
    
    # 宏观的多城策略
    multi_city_strategy: Optional[Dict[str, Any]]
    
    # 阶段A (交互式分析) 输出
    core_intent: Optional[Dict[str, Any]]
    attraction_preferences: Optional[Dict[str, Any]]
    food_preferences: Optional[Dict[str, Any]]
    accommodation_preferences: Optional[Dict[str, Any]]
    user_profile: Optional[UserProfile]
    
    # 用于在节点间传递和驱动前端语音播报的对话文本
    current_narration_text: Optional[str]
    
    # --- 自驾场景核心状态 ---
    # 车辆信息，在分析阶段早期从用户画像服务获取
    user_vehicle_info: Optional[Dict[str, Any]]
    # 驾驶策略标志，由分析节点设定，指导后续所有节点行为
    driving_strategy: Literal["range_aware", "general_assistance"]
    # 规划用的实际续航里程（已乘以保守系数）
    planning_range_km: Optional[float]
    # 续航保守系数，默认为一个标准值，未来可由用户交互调整
    range_buffer_factor: float
    
    # 阶段B (后台规划) 中间产物
    # 结构化存储每个城市的POI规划结果，取代旧的扁平列表
    city_plan_results: List[CityPlanResult]
    
    # 专门存储路线和充电规划的结果
    planned_charging_stops: Optional[List[Dict[str, Any]]]
    driving_routes: Optional[List[Dict[str, Any]]]
    
    # 阶段B (后台规划) 最终输出
    orchestrated_itinerary: Optional[Dict[str, Any]]
    final_itinerary: Optional[TravelItinerary]
    
    # 控制与错误处理
    # user_feedback 从 'proceed'/'cancel' 扩展为可以接收更复杂的用户输入
    user_feedback: Optional[Any]
    error: Optional[str]
    # 需要向用户澄清的信息类型, e.g., 'multi_city_strategy'
    # 如果无需澄清，则为None。这是交互模式的关键钩子。
    clarification_needed: Optional[str]
    
    # 进度跟踪
    current_step: Optional[str]
    progress_percentage: int
    
    # 工具调用记录
    tool_calls_log: List[Dict[str, Any]]
    
    # 记忆相关
    retrieved_memories: Optional[List[Dict[str, Any]]]
    new_memories: Optional[List[Dict[str, Any]]]
