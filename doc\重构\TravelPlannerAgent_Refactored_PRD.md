# Travel Planner Agent 重构版产品需求文档 (PRD)

## 1. 文档概述

### 1.1. 背景与目标

当前版本的旅行规划Agent功能强大，但与用户的交互是"黑盒"的，用户无法理解AI的决策过程，也无法在规划中途进行干预，导致信任感和可控性不足。

本次重构的核心产品目标是，将旅行规划Agent从一个"一步到位"的后台工具，升级为一个**透明、可交互、高度个性化**的智能规划伙伴。它将通过**分步展示思考过程**和**流式生成行程结果**两大核心体验，显著提升用户的信任感、参与度和最终满意度。

### 1.2. 核心场景：固定汽车自驾 (Core Scenario: Fixed Vehicle Self-Driving)
本次重构所有功能设计和交互流程，均围绕一个核心场景展开：**用户将使用自己的固定车辆进行自驾游**。这使得Agent的角色从通用旅行规划师，精准定位为一名专业的**"智能自驾领航员"**。因此，所有规划都必须将**路线合理性、驾驶便捷性、续航安全性**作为核心考量因素。

### 1.3. 核心用户故事

*   **As a** 注重出行体验的电动车主,
*   **I want** AI在规划长途旅行时，能像老司机一样帮我算好续航，并提前规划好沿途的充电站,
*   **So that** 我可以彻底摆脱续航焦虑，享受旅途本身，而不是一路都在找充电桩。

*   **As a** 每次到达景点或餐厅都要为找车位而烦恼的自驾用户,
*   **I want** AI在推荐任何地点时，都能同步告诉我那里的停车是否方便，甚至推荐最佳停车场,
*   **So that** 我可以节省大量时间，避免因停车问题破坏游玩心情。

*   **As a** 计划进行多城联游的用户,
*   **I want** AI能在我给出总天数后，智能地帮我建议在每个城市待几天，并规划好城市之间的交通衔接,
*   **So that** 我不用在几个城市的攻略之间反复横跳，能得到一个连贯、完整的旅行方案。

*   **As a** 希望掌控规划过程的用户,
*   **I want** 看到AI是如何分析我的驾驶需求，并如何一步步构建出完整的驾驶路线和行程的,
*   **So that** 我能即时了解规划进展，并对路线的合理性有稳定的预期。

## 2. 核心架构理念 (Core Architectural Concepts)

为了支撑更智能、更灵活的规划能力，本次重构引入两大核心架构理念。

### 2.1. 双模运行机制：交互式 vs. 全自动 (Dual-Mode Operation: Interactive vs. Automatic)

Agent必须支持两种截然不同的运行模式，以适应不同的业务场景：

*   **交互模式 (Interactive Mode)**: 这是面向真实用户的默认模式。在此模式下，Agent会在关键决策点（如多目的地策略、续航系数调整等）暂停，主动通过语音和UI展示其中间分析和建议，**并等待用户的明确确认**后才继续执行。这是实现"透明、可交互"产品目标的核心。

*   **全自动模式 (Fully Automatic Mode)**: 此模式主要为内部系统调用或无前端界面的场景设计。当请求中包含`"automatic": true`之类的标志时，Agent在遇到需要决策的节点时**不会暂停**。相反，它会依赖其内部的`策略规划师LLM`做出其认为最优的判断，然后无缝地继续执行后续步骤。这确保了Agent可以作为一项可预测的、自动化的服务被集成。

### 2.2. LLM角色矩阵：专业分工的"数字大脑" (LLM Role Matrix: A "Digital Brain" with Specialized Roles)

摒弃单一LLM处理所有任务的模糊模式，我们设计一个由三位"专家"组成的"LLM角色矩阵"，每个角色使用最适合其任务的特定模型，以实现效果、成本和速度的最佳平衡。

*   **策略规划师 (Strategic Planner)**:
    *   **使用模型**: 旗舰级大模型 (如 GLM-4, GPT-4)。
    *   **核心职责**: 负责最高层、最复杂的认知与推理任务，包括：多目的地宏观策略制定、复杂约束下的最终行程与路线编排（TSP问题）、充电桩智能插入等。**它的决策质量直接决定了规划方案的上限。**

*   **数据分析师 (Data Analyst)**:
    *   **使用模型**: 均衡型模型 (如 GLM-3-Turbo)。
    *   **核心职责**: 负责所有结构化的、有明确输入输出的分析任务，包括：从用户自然语言中提取意图（目的地、天数、预算等）、分析用户偏好、为POI列表进行打分和排序等。**它的准确性是保证规划方向正确的基础。**

*   **对话交互师 (Conversationalist)**:
    *   **使用模型**: 轻量级、高响应速度模型 (如 GLM-3-Turbo)。
    *   **核心职责**: 负责生成所有面向用户的、有"人味"的对话旁白。它将分析师输出的结构化数据，实时转化为自然、流畅、符合"智能自驾领航员"人设的语音文本。**它的存在是为了实现"陪伴式"的对话体验。**

## 3. 功能特性与交互流程

Agent的交互流程将演变为一个清晰的两阶段模式，完全对应新版UI的设计。

*   **阶段一：交互式分析 (UI左侧)**：Agent不再直接规划，而是首先通过流式输出，分步展示它对用户需求、画像和偏好的分析结果。在交互模式下，它会等待用户点击"立即规划"进行确认。
*   **阶段二：流式行程构建 (UI右侧)**：在用户确认或自动模式下决策通过后，Agent开始在UI右侧逐步、动态地构建最终的行程卡片，并同步更新一个可视化的进度状态。

---

## 4. 详细功能分解 (Functional Breakdown)

### 4.1. 数据流转核心理念: "作战室"与"档案室"

为了实现系统的实时性、鲁棒性和可追溯性，我们将采用多层数据存储策略：

*   **Redis (实时作战室)**: Redis将作为任务执行过程中的**高速缓存和状态管理器**。每个规划任务都会在Redis中有一个唯一的条目（如`task:{task_id}`），用于实时记录和更新Agent的**中间思考状态(State)**。**这种内存级的读写速度是保障前端能快速收到流式响应、避免卡顿的核心。** 这样做的好处是：1. **可观测性**：运维人员可以随时查看任何一个正在运行任务的当前状态。2. **鲁棒性**：如果Agent因故崩溃，可以从Redis中恢复任务的上下文，实现断点续传。

*   **MongoDB (完整档案室)**: MongoDB负责**永久归档完整的、未经删改的交互全过程**。一次任务结束后，后台会将本次交互的所有细节——包括用户的原始问题、每个节点的State快照、所有工具的调用与返回、LLM的输入输出原文——作为一个大的文档，完整存入MongoDB。**这一操作在后台异步完成，不会阻塞最终行程的返回。** 这为未来的模型精调、问题追溯、用户行为分析提供了最宝贵的原始数据。

*   **MySQL (结构化成果库)**: MySQL存储的是**最终的、结构化的、高价值的结果数据**。这包括用户可查看的**最终行程单**，以及通过LLM提炼出的、可用于提升未来服务质量的**用户长期记忆**和**更新后的用户画像**。**同理，所有向MySQL的写入都由后台任务异步执行，确保用户的主流程体验流畅。**

### 阶段A: 交互式分析 (用户发送请求后)

此阶段的核心是"将思考过程外化"，让用户看见AI的分析逻辑。它将包含一个新增的、至关重要的**宏观策略确认**环节。

| 步骤 | 功能描述 | 🤖 LLM 使用点 | 🧠 用户记忆调用点 | 💾 数据持久化点 | 🎤 语音反馈点 (新增) |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **A.0** | **宏观策略分析 (多目的地场景)** | **策略规划师LLM**: 1. **调用地图服务的"路线规划"工具计算城市间驾车距离和时间**。 2. **识别**出多个目的地。 3. 结合距离、总天数和城市特色，**生成宏观分配建议**。 4. **暂停等待用户确认(交互模式)；或采纳最优建议继续(自动模式)**。 | **是**：可调用用户历史多城游的偏好（如"用户更喜欢在一个城市深度游"）。 | **是 (Redis)**: 在State中记录`multi_city_strategy`，并根据模式设置`clarification_needed`标志。 | **(交互模式下，识别出多目的地时触发)**<br/>"好的，一个泉州和厦门的5日串联游！我查了一下，两个城市离得不远，开车大约1个多小时。为了让行程更从容，我建议可以先在历史名城泉州深度游玩2天，然后再去浪漫的海滨城市厦门享受3天。您觉得这个宏观安排怎么样？"<br/>**(等待用户确认/修改)** |
| **A.1** | **解析核心意图** | **数据分析师LLM**：从用户查询中提取主题、预算等实体。**（在宏观策略确认后执行）** | **是**：调用用户画像（MySQL/Redis），获取常驻地、历史偏好。 | **是 (Redis)**: 实时更新任务的中间状态。 | **对话交互师LLM**:<br/>"收到！一个去往[目的地]的[天数]日自驾游，听起来真不错。让我先为您分析一下行程和车辆情况。" |
| **A.2** | **分析驾驶情境** | **数据分析师LLM**。**根据车辆信息完备度，执行双模策略**：<br>1. **信息完整** (获取到续航): 设定为"精准续航规划"模式，并基于季节、路况等计算**规划续航**(如标称的80%)。<br>2. **信息不完整**: 设定为"通用驾驶辅助"模式。 | **是**: **优先调用**用户画像中的车辆信息(续航、充电规格)、历史驾驶习惯（如"该用户车辆冬季实际续航约为标称75%"）。 | **是 (Redis)**: 在State中记录`driving_strategy`、`planning_range_km`等关键决策。 | **对话交互师LLM (根据策略二选一)**:<br>**精准模式**: "检测到您的爱车是[车辆型号]，标称续航[450]公里。考虑到实际路况，我会按更稳妥的[360]公里为您规划充电，确保全程无忧。"<br>**通用模式**: "好的，这次自驾旅行，我会在每个目的地为您仔细寻找方便的停车场和充电站。由于暂时不清楚您车辆的具体续航，请您在路上留意电量哦。" |
| **A.3** | **分析景点偏好** | **数据分析师LLM**：结合核心意图和用户画像标签，推理出具体的景点类型偏好。 | **是**：调用用户画像中的兴趣标签（如"喜欢历史"、"偏好自然风光"）。 | **是 (Redis)**: 实时更新。 | **对话交互师LLM**:<br/>"根据您的喜好，我发现您对[景点偏好1]和[景点偏好2]特别感兴趣。我会重点为您留意这类地方，并确保它们停车方便。" |
| **A.4** | **分析美食偏好** | **数据分析师LLM**：推理出具体的美食偏好。 | **是**：调用用户画像中的饮食禁忌、消费习惯等。 | **是 (Redis)**: 实时更新。 | **对话交互师LLM**:<br/>"好的，在吃的方面，我会特别为您留意[口味偏好]的餐厅，并帮您看好附近的停车位。" |
| **A.5** | **分析住宿偏好** | **数据分析师LLM**：**增加强约束**：所有住宿推荐必须自带停车场或附近有稳定可靠的公共停车场。 | **是**：优先调用用户画像中的预算等级、消费习惯、历史住宿选择。 | **是 (Redis)**: 实时更新。 | **对话交互师LLM**:<br/>"住宿方面，我将为您寻找符合[预算范围]、[类型偏好]，并且**停车无忧**的酒店。" |

**用户交互点**: 所有分析步骤完成后，UI左侧展示完整的分析结果。
*   **交互模式**: Agent直接暂停，等待用户点击 **[立即规划]** 按钮，或对之前的分析提出修改意见。
*   **全自动模式**: Agent内部逻辑直接进入下一阶段，无需任何等待。
*   **未来交互预留**: 当需要用户确认"续航保守系数"或车辆信息时，Agent可在此处暂停等待用户回复，实现更深度的个性化。

---

### 阶段B: 流式行程构建 (用户确认或自动决策后)

此阶段的核心是"将构建过程可视化"，并从简单的POI组合，升级为**路线优化与驾驶服务规划**。

| 步骤 | 功能描述 | 🤖 LLM 使用点 | 🧠 用户记忆调用点 | 💾 数据持久化点 |
| :--- | :--- | :--- | :--- | :--- |
| **B.1** | **规划核心POI (景点/美食/住宿)** | **数据分析师LLM**：1. 工具获取候选POI。 2. 根据偏好、**停车便利性**、充电设施覆盖度进行综合打分排序。**在多目的地模式下，此步骤会按城市循环执行**。 | **否** (所需偏好已在State中) | **是 (Redis)**: 更新State |
| **B.2** | **路线优化与充电规划** | **策略规划师LLM (旗舰模型)**：**其核心任务是解决一个带约束的旅行商问题(TSP)**:<br>1. **输入**: 所有高质量POI、`driving_strategy`、`planning_range_km`、**`multi_city_strategy`**。 <br>2. **输出**: 空间和时间上最优的每日驾驶路线，并根据策略**智能插入充电站**，同时处理好**城市间的换乘日行程**。 | **否** | **是 (Redis)**: 更新State |
| **B.3** | **生成与归档** | **否**：将LLM编排好的结构化路线，格式化为最终的行程单。 | **否** | **是 (Redis)**: 更新最终行程。 |

---

### 阶段C: 归档与记忆沉淀 (异步解耦架构)

**设计依据**: 本阶段的设计严格遵循 **《融合系统架构.md》** 中定义的记忆体系，并采用工业级的**异步解耦**方案，以确保主流程的性能和系统的可靠性。

**核心理念**: 将"快速归档"和"耗时评估"分离。主流程（旅行规划）在完成对用户的响应后，应立即结束，其职责仅限于将原始日志存入MongoDB并触发一个后台任务。所有耗费计算资源的LLM分析和评估工作，全部由独立的后台任务异步完成。

#### 4.5.1. 异步工作流拆解

| 阶段 | 步骤 | 角色/组件 | 功能描述 | 触发方式 |
| :--- | :--- | :--- | :--- | :--- |
| **实时归档**<br/>(主流程) | **C.1: 快速归档** | `TravelPlannerAgent` | 在向用户返回最终行程后，立即从Redis中读取本次任务的完整上下文（对话、State变化、工具调用日志等），并将其作为一个**完整的JSON文档**存入**MongoDB**的`task_execution_logs`集合中。 | 用户任务成功结束时。 |
| | **C.2: 触发后台任务** | `TravelPlannerAgent` | 向一个任务队列（如Redis Pub/Sub）发布一条包含`task_id`的轻量级消息，通知后台可以开始进行记忆沉淀。 | C.1步骤成功后。 |
| **异步处理**<br/>(后台任务) | **C.3: 任务消费** | `记忆处理后台服务`<br/>(如Celery Worker) | 订阅任务队列，接收到`task_id`消息后，启动记忆沉淀流程。 | 由C.2步骤发布的消息触发。 |
| | **C.4: 记忆提取与评估** | `记忆生成/评估Agent` | 1. 根据`task_id`从**MongoDB**中读取完整的原始日志。<br/>2. 调用LLM进行分析，提取潜在的记忆点。<br/>3. 对每个记忆点进行**价值评分(1-5分)**。 | 由C.3步骤启动。 |
| | **C.5: 记忆持久化** | `记忆处理后台服务` | 将评分达到**4-5分**的高价值、结构化记忆，写入**MySQL**的`user_memories`表，并可能更新`user_profiles`表中的用户画像。 | C.4步骤成功完成后。 |

#### 4.5.2. 记忆沉淀标准与类别定义

为了确保记忆的质量和有效性，后台的`记忆生成Agent`和`记忆评估Agent`将遵循以下详细标准：

| 记忆类别 | 描述 | ✅ **应该记忆的示例 (高价值)** | ❌ **不应记忆的示例 (低价值)** | 🎯 **目标数据表/字段** |
| :--- | :--- | :--- | :--- | :--- |
| **驾驶习惯与车辆特性** | 用户的驾驶偏好或车辆在真实使用中的特性。 | "用户反馈冬季实际续航只有标称的70%。" (极高价值) <br/> "用户倾向于只使用官方品牌的超充站。" | "这次路上有点堵车。" (一次性事实) | `user_memories` (category: 'driving_habit', content: 'winter_range_factor_is_0.7') <br/> `user_profiles` (vehicle_preferences.charging_brand: 'official') |
| **出行方式偏好** | 用户对于长短途交通工具的选择习惯。 | "去稍远的地方我**不喜欢坐飞机**，觉得很麻烦。" <br/> "我周末出门**习惯自驾**。" | "这次我们是坐高铁去的。" (一次性事实) <br/> "去机场的路有点堵。" | `user_memories` (category: 'travel_style', content: 'avoids_flying_for_long_trips') <br/> `user_profiles` (travel_preferences.transport_mode: 'self_driving') |
| **住宿偏好** | 对住宿地点的类型、预算、品牌、设施的要求。 | "我出差**只住连锁品牌的经济型酒店**，比如汉庭、如家。" <br/> "我们家旅游，住宿预算可以到**1500元一晚**，要找五星级的。" | "这家酒店的早餐不错。" (具体评价) <br/> "希尔顿酒店在市中心。" (事实陈述) | `user_memories` (category: 'accommodation', content: 'prefers_chain_economy_hotels') <br/> `user_profiles` (accommodation_preferences.budget_level: 'luxury') |
| **餐饮偏好** | 对食物口味、餐厅类型、消费水平的偏好。 | "**不喜欢吃辣**，川菜湘菜都不要推荐。" <br/> "我们家聚餐**人均预算200元左右**。" | "今天中午吃的麦当劳。" (一次性事实) <br/> "这家日料店排队人好多。" | `user_memories` (category: 'food', content: 'dislikes_spicy_food') <br/> `user_profiles` (food_preferences.cuisine_exclusions: ['Sichuan', 'Hunan']) |
| **活动与兴趣** | 用户在旅行或日常中表现出的兴趣点。 | "我对**逛博物馆和科技馆**很感兴趣。" <br/> "孩子喜欢去有**大型滑梯的游乐园**。" | "今天下午去了公园。" (普通活动) <br/> "环球影城门票挺贵的。" | `user_memories` (category: 'interest', content: 'enjoys_museums') <br/> `user_profiles` (interest_tags: ['museum', 'tech', 'theme_park']) |
| **家庭与成员** | 用户的家庭构成或同行人信息。 | "**我有一个6岁的儿子**，规划时要考虑儿童活动。" <br/> "**这次是和父母一起出游**，行程不能太紧张。" | "我朋友也想去那里。" (非固定关系) <br/> "我们一家三口。" (不够具体) | `user_memories` (category: 'family', content: 'has_6_year_old_son') <br/> `user_profiles` (family_composition: {'children': [{'age': 6}]}) |
| **否定与纠正** | 用户明确表示的不满或纠正，是极高价值的负反馈。 | "**不要再给我推荐购物中心了**，我非常不喜欢逛街。" (评分5) <br/>"这个充电站是慢充，以后别推荐了。" | "这个推荐不太好。" (过于模糊) | `user_memories` (category: 'constraint', content: 'explicitly_dislikes_shopping_malls') <br/> `user_profiles` (activity_exclusions: ['shopping']) |

## 5. 非功能性需求

*   **性能要求**：所有流式事件（分析步骤、进度、行程卡片）的推送延迟必须在毫秒级，确保用户体验的流畅性。
*   **接口兼容性**：对现有前端的SSE通信协议（事件类型、核心数据结构）必须保持100%向后兼容。
*   **可靠性**：必须有明确的错误处理机制。当任意步骤失败时，应立即向前端发送错误事件，并终止流程，而不是无响应或崩溃。 
*   **对话质量要求 (新增)**: 为实现"陪伴式"体验，Agent生成的语音旁白必须由专门的LLM根据上下文动态生成，避免使用僵硬、重复的模板。文本需自然、友好，符合旅行规划师的专业人设。
*   **语音同步要求 (新增)**: 从后端生成分析结果到前端接收到对应的旁白文本，其延迟应足够低，以确保UI内容的更新与语音播报能够几乎同步发生，提供音画合一的流畅体验。 