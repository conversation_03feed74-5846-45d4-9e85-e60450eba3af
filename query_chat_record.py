"""
查询指定聊天记录ID的详细信息
用于分析聊天记录中的问题
"""

import asyncio
import json
from datetime import datetime
from src.database.mongodb_client import get_mongo_client
from src.database.mysql_client import get_db
from src.models.mysql_crud import ai_planning_session_crud

async def query_chat_record(record_id: str):
    """
    查询指定ID的聊天记录
    
    Args:
        record_id: 聊天记录ID (UUID格式)
    """
    print(f"🔍 查询聊天记录ID: {record_id}")
    print("=" * 60)
    
    # 1. 查询MongoDB中的AI交互日志
    try:
        mongo_client = await get_mongo_client()
        
        # 查询ai_interaction_logs集合
        interaction_log = await mongo_client.find_one(
            "ai_interaction_logs", 
            {"interaction_id": record_id}
        )
        
        if interaction_log:
            print("📋 MongoDB - AI交互日志:")
            print(f"  - 交互ID: {interaction_log.get('interaction_id')}")
            print(f"  - 用户ID: {interaction_log.get('user_id')}")
            print(f"  - 状态: {interaction_log.get('status')}")
            print(f"  - 时间戳: {interaction_log.get('timestamp')}")
            print(f"  - 应用来源: {interaction_log.get('application_source')}")
            
            # 显示业务步骤日志
            business_steps = interaction_log.get('business_steps_log', [])
            if business_steps:
                print(f"  - 业务步骤数: {len(business_steps)}")
                for i, step in enumerate(business_steps):
                    print(f"    步骤{i+1}: {step.get('step_name', 'N/A')} - {step.get('status', 'N/A')}")
            
            # 显示最终输出
            final_output = interaction_log.get('final_output')
            if final_output:
                print("  - 最终输出:")
                print(f"    {json.dumps(final_output, ensure_ascii=False, indent=4)}")
        else:
            print("❌ MongoDB - AI交互日志: 未找到记录")
            
    except Exception as e:
        print(f"❌ MongoDB查询失败: {str(e)}")
    
    # 2. 查询MongoDB中的对话记录
    try:
        conversation = await mongo_client.find_one(
            "conversations", 
            {"session_id": record_id}
        )
        
        if conversation:
            print("\n💬 MongoDB - 对话记录:")
            print(f"  - 会话ID: {conversation.get('session_id')}")
            print(f"  - 用户ID: {conversation.get('user_id')}")
            print(f"  - 创建时间: {conversation.get('created_at')}")
            
            messages = conversation.get('messages', [])
            print(f"  - 消息数量: {len(messages)}")
            for i, msg in enumerate(messages):
                print(f"    消息{i+1}: {msg.get('role')} - {msg.get('content')[:100]}...")
        else:
            print("❌ MongoDB - 对话记录: 未找到记录")
            
    except Exception as e:
        print(f"❌ MongoDB对话查询失败: {str(e)}")
    
    # 3. 查询MySQL中的规划会话
    try:
        async with get_db() as db:
            # 直接使用SQL查询
            from src.database.mysql_client import get_db_cursor
            async with get_db_cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM ai_planning_sessions WHERE id = %s",
                    (record_id,)
                )
                planning_session = await cursor.fetchone()

                if planning_session:
                    print("\n🗂️ MySQL - 规划会话:")
                    print(f"  - 会话ID: {planning_session.get('id')}")
                    print(f"  - 用户ID: {planning_session.get('user_id')}")
                    print(f"  - 用户输入: {planning_session.get('user_input')}")
                    print(f"  - 创建时间: {planning_session.get('created_at')}")
                    print(f"  - 状态: {planning_session.get('status', 'N/A')}")
                else:
                    print("❌ MySQL - 规划会话: 未找到记录")

    except Exception as e:
        print(f"❌ MySQL查询失败: {str(e)}")
    
    # 4. 查询MongoDB中的行程记录
    try:
        itinerary = await mongo_client.find_one(
            "itineraries", 
            {"trace_id": record_id}
        )
        
        if itinerary:
            print("\n🗺️ MongoDB - 行程记录:")
            print(f"  - 追踪ID: {itinerary.get('trace_id')}")
            print(f"  - 用户ID: {itinerary.get('user_id')}")
            print(f"  - 标题: {itinerary.get('title')}")
            print(f"  - 状态: {itinerary.get('status')}")
            print(f"  - 创建时间: {itinerary.get('created_at')}")
            
            # 显示行程详情
            itinerary_data = itinerary.get('itinerary_data')
            if itinerary_data:
                print("  - 行程详情:")
                print(f"    {json.dumps(itinerary_data, ensure_ascii=False, indent=4)}")
        else:
            print("❌ MongoDB - 行程记录: 未找到记录")
            
    except Exception as e:
        print(f"❌ MongoDB行程查询失败: {str(e)}")

async def main():
    """主函数"""
    record_id = "a090c760-4eb6-4772-b8a3-079fdfa4c67b"
    await query_chat_record(record_id)

if __name__ == "__main__":
    asyncio.run(main())
