"""
LangGraph 工作流图定义 (V2.0 - 事件驱动架构)

实现基于LangGraph的旅行规划工作流，集成NotificationService进行事件驱动的前端推送。
遵循推送.md文档要求的解耦架构设计。
"""

import asyncio
from typing import Dict, Any, Optional
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from .state import TravelPlanState
from .nodes import (
    analyze_core_intent,
    analyze_multi_city_strategy, 
    analyze_driving_context,
    analyze_preferences,
    execute_planning_stage,
    finalize_result
)
from src.core.logger import get_logger

logger = get_logger("travel_planner_graph")


def should_continue_to_planning(state: TravelPlanState) -> str:
    """
    条件路由函数：决定是否继续到规划阶段
    
    检查分析阶段是否完成，以及是否有错误发生
    """
    # 检查是否有错误
    if state.get("error"):
        logger.warning(f"检测到错误，终止工作流: {state.get('error')}")
        return END
    
    # 检查核心意图是否成功解析
    core_intent = state.get("core_intent")
    if not core_intent or not core_intent.get("destinations"):
        logger.warning("核心意图解析失败，终止工作流")
        return END
    
    # 检查是否需要用户澄清（交互模式预留）
    if state.get("clarification_needed"):
        logger.info("需要用户澄清，暂停工作流")
        return END
    
    # 继续到规划阶段
    return "execute_planning_stage"


def create_travel_planner_graph(enable_checkpointing: bool = False) -> StateGraph:
    """
    创建旅行规划LangGraph工作流
    
    Args:
        enable_checkpointing: 是否启用检查点功能（用于任务恢复）
        
    Returns:
        编译后的LangGraph工作流
    """
    logger.info("创建旅行规划LangGraph工作流")
    
    # 创建状态图
    workflow = StateGraph(TravelPlanState)
    
    # === 添加节点 ===
    
    # 分析阶段节点
    workflow.add_node("analyze_core_intent", analyze_core_intent)
    workflow.add_node("analyze_multi_city_strategy", analyze_multi_city_strategy)
    workflow.add_node("analyze_driving_context", analyze_driving_context)
    workflow.add_node("analyze_preferences", analyze_preferences)
    
    # 规划阶段节点
    workflow.add_node("execute_planning_stage", execute_planning_stage)
    workflow.add_node("finalize_result", finalize_result)
    
    # === 定义边和流程 ===
    
    # 设置入口点
    workflow.set_entry_point("analyze_core_intent")
    
    # 分析阶段：顺序执行
    workflow.add_edge("analyze_core_intent", "analyze_multi_city_strategy")
    workflow.add_edge("analyze_multi_city_strategy", "analyze_driving_context")
    workflow.add_edge("analyze_driving_context", "analyze_preferences")
    
    # 分析完成后的条件路由
    workflow.add_conditional_edges(
        "analyze_preferences",
        should_continue_to_planning,
        {
            "execute_planning_stage": "execute_planning_stage",
            END: END
        }
    )
    
    # 规划阶段：顺序执行
    workflow.add_edge("execute_planning_stage", "finalize_result")
    workflow.add_edge("finalize_result", END)
    
    # === 编译工作流 ===
    
    # 配置检查点（可选）
    checkpointer = None
    if enable_checkpointing:
        # 使用内存检查点（生产环境可以使用Redis检查点）
        checkpointer = MemorySaver()
        logger.info("启用LangGraph检查点功能")
    
    # 编译图
    compiled_graph = workflow.compile(checkpointer=checkpointer)
    
    logger.info("LangGraph工作流创建完成")
    return compiled_graph


class TravelPlannerGraph:
    """
    旅行规划LangGraph包装器
    
    提供高级接口来执行旅行规划工作流，支持流式和非流式执行。
    """
    
    def __init__(self, enable_checkpointing: bool = False):
        """
        初始化图包装器
        
        Args:
            enable_checkpointing: 是否启用检查点功能
        """
        self.graph = create_travel_planner_graph(enable_checkpointing)
        self.enable_checkpointing = enable_checkpointing
        logger.info("TravelPlannerGraph初始化完成")
    
    async def run_automatic(
        self,
        user_id: str,
        query: str,
        task_id: str,
        notification_service,
        **kwargs
    ) -> Dict[str, Any]:
        """
        运行完整的自动规划流程（非流式）
        
        Args:
            user_id: 用户ID
            query: 用户查询
            task_id: 任务ID
            notification_service: 通知服务实例
            **kwargs: 其他参数
            
        Returns:
            最终状态
        """
        logger.info(f"开始自动规划流程 - 任务: {task_id}")
        
        # 创建初始状态
        initial_state = self._create_initial_state(
            user_id=user_id,
            query=query,
            task_id=task_id,
            notification_service=notification_service,
            **kwargs
        )
        
        # 执行工作流
        config = {"thread_id": task_id} if self.enable_checkpointing else {}
        final_state = await self.graph.ainvoke(initial_state, config=config)
        
        logger.info(f"自动规划流程完成 - 任务: {task_id}")
        return final_state

    async def run_planning_only(
        self,
        user_id: str,
        query: str,
        task_id: str,
        analysis_result: dict,
        notification_service,
        **kwargs
    ) -> Dict[str, Any]:
        """
        只运行规划阶段，使用已有的分析结果

        Args:
            user_id: 用户ID
            query: 用户查询
            task_id: 任务ID
            analysis_result: 已有的分析结果
            notification_service: 通知服务实例
            **kwargs: 其他参数

        Returns:
            最终状态
        """
        logger.info(f"开始规划阶段流程 - 任务: {task_id}")

        # 创建包含分析结果的初始状态
        initial_state = self._create_initial_state_with_analysis(
            user_id=user_id,
            query=query,
            task_id=task_id,
            analysis_result=analysis_result,
            notification_service=notification_service,
            **kwargs
        )

        # 直接从规划阶段开始执行
        from .nodes import execute_planning_stage, finalize_result

        # 执行规划阶段
        planning_result = await execute_planning_stage(initial_state)
        initial_state.update(planning_result)

        # 执行结果整理
        final_result = await finalize_result(initial_state)
        initial_state.update(final_result)

        logger.info(f"规划阶段流程完成 - 任务: {task_id}")
        return initial_state

    async def stream_automatic(
        self,
        user_id: str,
        query: str,
        task_id: str,
        notification_service,
        **kwargs
    ):
        """
        运行流式自动规划流程
        
        Args:
            user_id: 用户ID
            query: 用户查询
            task_id: 任务ID
            notification_service: 通知服务实例
            **kwargs: 其他参数
            
        Yields:
            状态更新流
        """
        logger.info(f"开始流式自动规划流程 - 任务: {task_id}")
        
        # 创建初始状态
        initial_state = self._create_initial_state(
            user_id=user_id,
            query=query,
            task_id=task_id,
            notification_service=notification_service,
            **kwargs
        )
        
        # 执行流式工作流
        config = {"thread_id": task_id} if self.enable_checkpointing else {}
        
        async for state_update in self.graph.astream(initial_state, config=config):
            yield state_update
        
        logger.info(f"流式自动规划流程完成 - 任务: {task_id}")
    
    def _create_initial_state(
        self,
        user_id: str,
        query: str,
        task_id: str,
        notification_service,
        **kwargs
    ) -> TravelPlanState:
        """
        创建初始状态
        
        Args:
            user_id: 用户ID
            query: 用户查询
            task_id: 任务ID
            notification_service: 通知服务实例
            **kwargs: 其他参数
            
        Returns:
            初始状态字典
        """
        return {
            "trace_id": task_id,
            "task_id": task_id,
            "user_id": user_id,
            "original_query": query,
            "notification_service": notification_service,
            "execution_mode": "automatic",
            "destinations": [],
            "multi_city_strategy": None,
            "core_intent": None,
            "attraction_preferences": None,
            "food_preferences": None,
            "accommodation_preferences": None,
            "user_profile": None,
            "current_narration_text": None,
            "user_vehicle_info": kwargs.get("vehicle_info"),
            "driving_strategy": "general_assistance",
            "planning_range_km": None,
            "range_buffer_factor": 0.8,
            "city_plan_results": [],
            "planned_charging_stops": None,
            "driving_routes": None,
            "orchestrated_itinerary": None,
            "final_itinerary": None,
            "user_feedback": None,
            "error": None,
            "clarification_needed": None,
            "current_step": "初始化",
            "progress_percentage": 0,
            "tool_calls_log": [],
            "retrieved_memories": None,
            "new_memories": None
        }

    def _create_initial_state_with_analysis(
        self,
        user_id: str,
        query: str,
        task_id: str,
        analysis_result: dict,
        notification_service,
        **kwargs
    ) -> TravelPlanState:
        """
        创建包含分析结果的初始状态

        Args:
            user_id: 用户ID
            query: 用户查询
            task_id: 任务ID
            analysis_result: 已有的分析结果
            notification_service: 通知服务实例
            **kwargs: 其他参数

        Returns:
            包含分析结果的初始状态字典
        """
        # 先创建基础状态
        state = self._create_initial_state(
            user_id=user_id,
            query=query,
            task_id=task_id,
            notification_service=notification_service,
            **kwargs
        )

        # 填充分析结果
        core_intent = analysis_result.get("core_intent", {})
        state.update({
            "destinations": core_intent.get("destinations", []),
            "core_intent": core_intent,
            "attraction_preferences": analysis_result.get("attraction_preferences"),
            "food_preferences": analysis_result.get("food_preferences"),
            "accommodation_preferences": analysis_result.get("accommodation_preferences"),
            "user_profile": analysis_result.get("user_profile"),
            "current_step": "分析完成，开始规划",
            "progress_percentage": 50
        })

        return state
