-- =================================================================
-- Digital Human - User Profile Schema Initialization Script
-- =================================================================
-- 项目: 数字人 (Digital Human Project)
-- Schema: dh_user_profile (用户档案中心)
-- 描述: 本脚本将完整地创建用户档案中心所需的所有表结构。
--       它会先安全地删除已存在的旧表，然后再进行创建，
--       因此可以重复执行以重置数据库。
-- =================================================================

-- 如果 Schema 不存在，则创建
CREATE SCHEMA IF NOT EXISTS `dh_user_profile` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 切换到目标 Schema
USE `dh_user_profile`;

-- -----------------------------------------------------
-- 第一步: 安全删除已存在的旧表
-- (按逆向依赖顺序删除，防止潜在问题)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `user_vehicle_bindings`;
DROP TABLE IF EXISTS `user_summaries`;
DROP TABLE IF EXISTS `user_memories`;
DROP TABLE IF EXISTS `credentials`;
DROP TABLE IF EXISTS `vehicles`;
DROP TABLE IF EXISTS `users`;
DROP TABLE IF EXISTS `user_favorites`;
DROP TABLE IF EXISTS `favorite_tags`;


-- -----------------------------------------------------
-- 第二步: 创建所有新表
-- -----------------------------------------------------

-- 表 1: `users` - 核心用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键, 自增',
  `nickname` VARCHAR(50) NOT NULL COMMENT '用户昵称',
  `avatar_url` VARCHAR(255) NULL COMMENT '用户头像的URL',
  `status` ENUM('ACTIVE', 'SUSPENDED', 'DELETED') NOT NULL DEFAULT 'ACTIVE' COMMENT '账户状态 (ACTIVE: 正常, SUSPENDED: 暂停, DELETED: 删除)',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '账户创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '账户信息最后更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='核心用户表';

-- 表 2: `vehicles` - 车辆信息表
CREATE TABLE IF NOT EXISTS `vehicles` (
  `vin` VARCHAR(17) NOT NULL COMMENT '主键, 车辆识别代号 (Vehicle Identification Number), 全球唯一',
  `license_plate` VARCHAR(20) NULL COMMENT '车牌号',
  `brand` VARCHAR(50) NULL COMMENT '车辆品牌',
  `model` VARCHAR(50) NULL COMMENT '车辆型号',
  `activated_at` TIMESTAMP NULL COMMENT '车辆首次在系统中被激活的时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  PRIMARY KEY (`vin`)
) ENGINE=InnoDB COMMENT='车辆信息表';

-- 表 3: `credentials` - 用户凭证表 (身份翻译官)
CREATE TABLE IF NOT EXISTS `credentials` (
  `user_id` INT NOT NULL COMMENT '关联到 users.id，标识该凭证属于哪个用户',
  `provider` VARCHAR(20) NOT NULL COMMENT '认证提供方 (例如: ''vehicle_os'', ''local'', ''wechat'')',
  `identifier` VARCHAR(100) NOT NULL COMMENT '认证唯一标识 (例如: 车机系统的用户ID, 手机号, openid)',
  `secret` VARCHAR(255) NULL COMMENT '认证密钥 (例如: 哈希后的密码, 第三方或车机登录时可为空)',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '凭证创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '凭证最后更新时间',
  PRIMARY KEY (`user_id`, `provider`),
  UNIQUE INDEX `idx_provider_identifier` (`provider`, `identifier` ASC) COMMENT '确保同一认证提供方下的标识是唯一的'
) ENGINE=InnoDB COMMENT='用户凭证表, 与外部系统站账号关系映射';

-- 表 4: `user_memories` - AI长期记忆表
CREATE TABLE IF NOT EXISTS `user_memories` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '记忆ID, 主键, 自增',
  `user_id` INT NOT NULL COMMENT '关联到 users.id，标识该记忆属于哪个用户',
  `memory_content` TEXT NOT NULL COMMENT '记忆内容 (一段关于用户的事实性陈述)，示例：
"用户对海鲜过敏。"
"用户的孩子叫小明，今年5岁。"
"用户最喜欢的城市是京都。"
"用户正在计划一次预算不超过8000元的旅行。"
"用户提到他不喜欢夏天潮湿的天气。"
"用户的工作是软件工程师。"',
  `source_session_id` VARCHAR(50) NULL COMMENT '来源会话ID (关联到MongoDB中的对话历史，用于追溯)',
  `confidence` FLOAT NOT NULL DEFAULT 1.0 COMMENT 'AI对该记忆的置信度 (0.0 到 1.0)',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记忆形成时间',
  `last_accessed` TIMESTAMP NULL COMMENT '记忆最近一次被AI检索的时间',
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id` ASC) COMMENT '为用户ID创建索引以加速查询'
) ENGINE=InnoDB COMMENT='AI长期记忆表，存储AI从对话中学习到的关于用户的关键信息';

-- 表 5: `user_summaries` - 用户画像摘要表
CREATE TABLE IF NOT EXISTS `user_summaries` (
  `user_id` INT NOT NULL COMMENT '主键, 逻辑外键关联到 users.id，一个用户只对应一条摘要记录',
  `summary` TEXT NOT NULL COMMENT '用户画像摘要 (由AI生成的自然语言描述)，示例：
"该用户是一位热爱摄影和徒步的旅行者，对预算较为敏感，喜欢安静的自然风光而非喧闹的都市。"',
  `keywords` JSON NOT NULL COMMENT '从摘要中提取的关键词数组 (JSON数组)，示例：["摄影", "徒步", "预算敏感", "自然风光", "安静偏好"] ',
  `model_version` VARCHAR(50) NULL COMMENT '生成此摘要的AI模型版本号',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '摘要的最后更新时间',
  PRIMARY KEY (`user_id`),
  -- 关键索引: 为JSON数组创建多值索引，以支持高效检索。需要 MySQL 8.0.17+
  INDEX `idx_summaries_keywords` ((CAST(keywords AS CHAR(50) ARRAY)))
) ENGINE=InnoDB COMMENT='用户画像摘要表';

-- 表 6: `user_vehicle_bindings` - 用户车辆绑定关系表
CREATE TABLE IF NOT EXISTS `user_vehicle_bindings` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键, 自增',
  `user_id` INT NOT NULL COMMENT '关联到 users.id',
  `vehicle_vin` VARCHAR(17) NOT NULL COMMENT '关联到 vehicles.vin',
  `role` ENUM('OWNER', 'FAMILY', 'GUEST') NOT NULL DEFAULT 'GUEST' COMMENT '用户在此车中的角色 (OWNER:车主, FAMILY:家庭成员, GUEST:访客)',
  `status` ENUM('ACTIVE', 'PENDING', 'INACTIVE') NOT NULL DEFAULT 'PENDING' COMMENT '绑定状态 (ACTIVE:生效中, PENDING:待确认, INACTIVE:已解绑)',
  `bind_at` TIMESTAMP NULL COMMENT '绑定关系生效的时间',
  `unbind_at` TIMESTAMP NULL COMMENT '绑定关系解除的时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_user_vehicle_unique` (`user_id`, `vehicle_vin`) COMMENT '确保一个用户和一辆车之间只有一条绑定关系',
  INDEX `idx_user_id` (`user_id` ASC),
  INDEX `idx_vehicle_vin` (`vehicle_vin` ASC)
) ENGINE=InnoDB COMMENT='用户与车辆的绑定关系表';

-- 表 7: `favorite_tags` - 收藏标签字典表 (已增加应用来源区分)
CREATE TABLE IF NOT EXISTS `favorite_tags` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键, 自增',
  `application_source` VARCHAR(50) NOT NULL COMMENT '该标签所属的应用 (例如: dh_tripplanner, dh_ailab)',
  `tag_name` VARCHAR(50) NOT NULL COMMENT '标签名称 (例如: 旅行偏好, Prompt技巧)',
  `weight_multiplier` FLOAT NOT NULL DEFAULT 1.0 COMMENT '权重乘数, 用于AI记忆分析',
  `description` TEXT NULL COMMENT '关于此标签的详细描述',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_app_tag_unique` (`application_source`, `tag_name`) COMMENT '确保同一个应用下的标签名是唯一的'
) ENGINE=InnoDB COMMENT='统一收藏标签字典表，通过application_source区分领域';


-- 表 8: `user_favorites` - 用户收藏记录表
CREATE TABLE IF NOT EXISTS `user_favorites` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '收藏记录主键, 自增',
  `user_id` INT NOT NULL COMMENT '逻辑外键, 关联到 users.id',
  `interaction_id` VARCHAR(36) NOT NULL COMMENT '核心关联键, 关联到 dh_platform_data.ai_interaction_logs 中的任务ID',
  `tag_id` INT NOT NULL COMMENT '逻辑外键, 关联到 favorite_tags.id',
  `selected_content` TEXT NULL COMMENT '用户收藏的具体文本摘录, 用于快速预览',
  `user_notes` TEXT NULL COMMENT '用户为本次收藏添加的个人笔记或批注',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏创建时间',
  PRIMARY KEY (`id`),
  INDEX `idx_user_id_created_at` (`user_id`, `created_at` DESC) COMMENT '快速获取用户的收藏列表'
) ENGINE=InnoDB COMMENT='统一的用户收藏记录表';

-- =================================================================
-- Digital Human - User Profile Schema Migration Script
-- Version: 1.1.0
-- 描述: 本脚本为 dh_user_profile 数据库增加积分和VPA形象槽位功能。
--       这是一个增量更新脚本，可以安全地在现有数据库上执行。
-- =================================================================

USE `dh_user_profile`;

-- -----------------------------------------------------
-- 步骤 1: 为 `users` 表增加新字段
-- -----------------------------------------------------
-- 检查 `points_balance` 字段是否存在，如果不存在则添加
DELIMITER //
CREATE PROCEDURE AddPointsBalanceColumn()
BEGIN
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE table_schema=DATABASE() AND table_name='users' AND column_name='points_balance') THEN
        ALTER TABLE `users` ADD COLUMN `points_balance` INT NOT NULL DEFAULT 0 COMMENT '用户当前积分余额' AFTER `status`;
    END IF;
END //
DELIMITER ;
CALL AddPointsBalanceColumn();
DROP PROCEDURE AddPointsBalanceColumn;

-- 检查 `avatar_slot_limit` 字段是否存在，如果不存在则添加
DELIMITER //
CREATE PROCEDURE AddAvatarSlotLimitColumn()
BEGIN
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE table_schema=DATABASE() AND table_name='users' AND column_name='avatar_slot_limit') THEN
        ALTER TABLE `users` ADD COLUMN `avatar_slot_limit` INT NOT NULL DEFAULT 3 COMMENT '用户拥有的数字人形象槽位上限' AFTER `points_balance`;
    END IF;
END //
DELIMITER ;
CALL AddAvatarSlotLimitColumn();
DROP PROCEDURE AddAvatarSlotLimitColumn;


-- -----------------------------------------------------
-- 步骤 2: 创建积分系统所需的新表
-- -----------------------------------------------------

-- 表 1: `point_transaction_types` - 积分交易类型字典表
CREATE TABLE IF NOT EXISTS `point_transaction_types` (
  `id` INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `type_key` VARCHAR(50) NOT NULL COMMENT '交易类型的唯一英文标识',
  `description_template` VARCHAR(255) NOT NULL COMMENT '交易类型的描述模板',
  `direction` ENUM('INCOME', 'EXPENSE') NOT NULL COMMENT '资金方向 (收入/支出)',
  UNIQUE INDEX `idx_type_key_unique` (`type_key`)
) ENGINE=InnoDB COMMENT='用户积分交易类型字典表';

-- 表 2: `point_transactions` - 用户积分收支明细表
CREATE TABLE IF NOT EXISTS `point_transactions` (
  `id` BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NOT NULL COMMENT '关联到 users.id',
  `transaction_type_id` INT NOT NULL COMMENT '关联到 point_transaction_types.id',
  `amount` INT NOT NULL COMMENT '积分变动数量 (正/负)',
  `balance_after` INT NOT NULL COMMENT '本次交易后用户的积分余额',
  `description` VARCHAR(255) NOT NULL COMMENT '根据模板生成的具体交易描述',
  `related_entity_id` VARCHAR(50) NULL COMMENT '关联的实体ID (技能ID, 形象实例ID等)',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_user_id_created_at` (`user_id`, `created_at` DESC)
) ENGINE=InnoDB COMMENT='用户积分收支明细表';