"""
增强版实时通知服务 (NotificationService V2.0)

基于推送.md文档要求实现的核心通知服务，负责：
1. 将业务事件发布到Redis的Pub/Sub频道
2. 同步更新Redis中的持久化任务状态 
3. 支持完整的任务生命周期管理
4. 提供标准化的SSE事件格式
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional

from src.database.redis_client import RedisClient

logger = logging.getLogger(__name__)


class NotificationService:
    """
    增强版实时通知服务
    
    实现事件驱动架构，通过Redis Pub/Sub解耦业务逻辑与推送机制，
    同时维护持久化的任务状态以支持可观测性和恢复能力。
    """
    
    def __init__(self, redis_client: RedisClient, task_ttl: int = 3600):
        """
        初始化通知服务
        
        Args:
            redis_client: Redis客户端实例
            task_ttl: 任务状态TTL，默认1小时
        """
        self.redis = redis_client.client if hasattr(redis_client, 'client') else redis_client
        self.task_ttl = task_ttl
        self.logger = logger

    async def _publish_event(self, task_id: str, event_data: Dict[str, Any]):
        """
        发布事件到Redis Pub/Sub频道
        
        Args:
            task_id: 任务ID
            event_data: 事件数据
        """
        channel = f"task_channel:{task_id}"
        message = json.dumps(event_data, ensure_ascii=False)
        await self.redis.publish(channel, message)
        self.logger.debug(f"事件已发布到频道 {channel}: {event_data.get('event', 'unknown')}")

    async def _update_task_status(self, task_id: str, updates: Dict[str, Any]):
        """
        更新Redis HASH中的任务状态
        
        Args:
            task_id: 任务ID
            updates: 更新的状态字段
        """
        key = f"task_status:{task_id}"
        updates["last_updated"] = datetime.utcnow().isoformat()
        
        # 确保所有值都可序列化
        serializable_updates = {}
        for field, value in updates.items():
            if value is None:
                serializable_updates[field] = ""
            elif isinstance(value, dict) or isinstance(value, list):
                serializable_updates[field] = json.dumps(value, ensure_ascii=False)
            else:
                serializable_updates[field] = str(value)
        
        await self.redis.hset(key, mapping=serializable_updates)
        # 每次更新都刷新TTL，防止正常任务过期
        await self.redis.expire(key, self.task_ttl)
        self.logger.debug(f"任务状态已更新: {task_id}, TTL: {self.task_ttl}s")

    async def initialize_task(self, task_id: str):
        """
        初始化任务状态并设置TTL
        
        Args:
            task_id: 任务ID
        """
        initial_state = {
            "overall_status": "pending",
            "current_step": "initialization",
            "steps_status": json.dumps({})
        }
        
        await self._update_task_status(task_id, initial_state)
        self.logger.info(f"任务 {task_id} 状态已初始化，TTL: {self.task_ttl}s")

    async def notify_step_start(self, task_id: str, step_name: str, title: str, message: str):
        """
        推送步骤开始事件
        
        Args:
            task_id: 任务ID
            step_name: 步骤名称（如 'core_intent_analysis'）
            title: 显示标题
            message: 状态消息
        """
        # 构建标准SSE事件格式
        event = {
            "event": "step_start",
            "data": {
                "step_id": f"{step_name}_{task_id}",
                "step_name": step_name,
                "title": title,
                "message": message
            }
        }
        
        # 发布事件
        await self._publish_event(task_id, event)
        
        # 更新任务状态
        status_update = {
            "overall_status": "running",
            "current_step": step_name
        }
        
        # 更新步骤状态
        key = f"task_status:{task_id}"
        steps_status_raw = await self.redis.hget(key, "steps_status")
        steps_status = json.loads(steps_status_raw) if steps_status_raw else {}
        steps_status[step_name] = {
            "status": "running",
            "start_time": datetime.utcnow().isoformat(),
            "title": title,
            "message": message
        }
        status_update["steps_status"] = json.dumps(steps_status)
        
        await self._update_task_status(task_id, status_update)
        self.logger.info(f"步骤开始: {step_name} ({task_id}) - {title}")

    async def notify_step_end(self, task_id: str, step_name: str, status: str, result: Dict[str, Any] = None):
        """
        推送步骤结束事件
        
        Args:
            task_id: 任务ID
            step_name: 步骤名称
            status: 状态（'success' 或 'failed'）
            result: 步骤结果数据
        """
        # 构建标准SSE事件格式
        event = {
            "event": "step_end",
            "data": {
                "step_id": f"{step_name}_{task_id}",
                "step_name": step_name,
                "status": status,
                "result": result or {}
            }
        }
        
        # 发布事件
        await self._publish_event(task_id, event)
        
        # 更新步骤状态
        key = f"task_status:{task_id}"
        steps_status_raw = await self.redis.hget(key, "steps_status")
        steps_status = json.loads(steps_status_raw) if steps_status_raw else {}
        if step_name in steps_status:
            steps_status[step_name]["status"] = status
            steps_status[step_name]["end_time"] = datetime.utcnow().isoformat()
            if result:
                steps_status[step_name]["result"] = result
        
        await self._update_task_status(task_id, {"steps_status": json.dumps(steps_status)})
        self.logger.info(f"步骤结束: {step_name} ({task_id}) - {status}")

    async def notify_final_result(self, task_id: str, final_data: Dict[str, Any]):
        """
        推送规划完成事件
        
        Args:
            task_id: 任务ID
            final_data: 最终结果数据
        """
        # 发布完成事件
        complete_event = {"event": "complete", "data": final_data}
        await self._publish_event(task_id, complete_event)
        
        # 发布流结束信号
        eos_event = {"event": "eos"}
        await self._publish_event(task_id, eos_event)
        
        # 更新最终状态
        status_update = {
            "overall_status": "completed",
            "current_step": "finished",
            "final_result": json.dumps(final_data)
        }
        await self._update_task_status(task_id, status_update)
        self.logger.info(f"任务完成: {task_id}")

    async def notify_error(self, task_id: str, error_message: str, step_name: str = "unknown"):
        """
        推送错误事件
        
        Args:
            task_id: 任务ID
            error_message: 错误消息
            step_name: 出错的步骤名称
        """
        # 发布错误事件
        error_event = {
            "event": "error",
            "data": {
                "step_name": step_name,
                "message": error_message
            }
        }
        await self._publish_event(task_id, error_event)
        
        # 发布流结束信号
        eos_event = {"event": "eos"}
        await self._publish_event(task_id, eos_event)
        
        # 更新错误状态
        status_update = {
            "overall_status": "failed",
            "current_step": step_name,
            "error_info": error_message
        }
        await self._update_task_status(task_id, status_update)
        self.logger.error(f"任务失败: {task_id}, 错误: {error_message}")

    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态字典，如果任务不存在则返回None
        """
        key = f"task_status:{task_id}"
        data = await self.redis.hgetall(key)
        
        if not data:
            return None
        
        # 反序列化JSON字段
        result = {}
        for field, value in data.items():
            if field in ['steps_status', 'final_result'] and value:
                try:
                    result[field] = json.loads(value)
                except json.JSONDecodeError:
                    result[field] = value
            else:
                result[field] = value if value != "" else None
        
        return result

    async def cleanup_task(self, task_id: str) -> bool:
        """
        清理任务数据
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功清理
        """
        try:
            key = f"task_status:{task_id}"
            await self.redis.delete(key)
            self.logger.info(f"任务数据已清理: {task_id}")
            return True
        except Exception as e:
            self.logger.error(f"清理任务数据失败: {task_id}, 错误: {str(e)}")
            return False 