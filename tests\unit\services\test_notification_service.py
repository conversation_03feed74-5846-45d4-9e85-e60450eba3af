"""
NotificationService 单元测试

使用pytest-asyncio和模拟Redis客户端，验证增强版NotificationService的所有功能。
"""

import json
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from src.services.notification_service import NotificationService


@pytest.fixture
def mock_redis():
    """模拟Redis客户端"""
    # 这个mock模拟的是redis.asyncio.Redis实例
    mock_redis_instance = AsyncMock()
    mock_redis_instance.publish = AsyncMock()
    mock_redis_instance.hset = AsyncMock()
    mock_redis_instance.hget = AsyncMock()
    mock_redis_instance.hgetall = AsyncMock()
    mock_redis_instance.expire = AsyncMock()
    mock_redis_instance.delete = AsyncMock()
    
    # 这个顶层mock模拟的是我们自己封装的RedisClient
    mock_redis_client = AsyncMock()
    mock_redis_client.client = mock_redis_instance
    return mock_redis_client


@pytest.fixture
def notification_service(mock_redis):
    """NotificationService实例"""
    return NotificationService(redis_client=mock_redis, task_ttl=3600)


class TestNotificationService:
    """NotificationService测试类"""

    @pytest.mark.asyncio
    async def test_initialize_task(self, notification_service, mock_redis):
        """测试任务初始化"""
        task_id = "test_task_123"
        
        await notification_service.initialize_task(task_id)
        
        # 验证hset被调用
        mock_redis.client.hset.assert_called_once()
        call_args = mock_redis.client.hset.call_args
        assert call_args[0][0] == f"task_status:{task_id}"
        
        # 验证mapping参数
        mapping = call_args[1]["mapping"]
        assert mapping["overall_status"] == "pending"
        assert mapping["current_step"] == "initialization"
        assert json.loads(mapping["steps_status"]) == {}
        assert "last_updated" in mapping
        
        # 验证expire被调用
        mock_redis.client.expire.assert_called_once_with(f"task_status:{task_id}", 3600)

    @pytest.mark.asyncio
    async def test_notify_step_start(self, notification_service, mock_redis):
        """测试步骤开始通知"""
        task_id = "test_task_123"
        step_name = "core_intent_analysis"
        title = "解析用户需求"
        message = "正在分析您的旅行意图..."
        
        # 模拟Redis hget返回空的steps_status
        mock_redis.client.hget.return_value = "{}"
        
        await notification_service.notify_step_start(task_id, step_name, title, message)
        
        # 验证publish被调用
        mock_redis.client.publish.assert_called_once()
        call_args = mock_redis.client.publish.call_args
        assert call_args[0][0] == f"task_channel:{task_id}"
        
        # 验证发布的事件格式
        published_event = json.loads(call_args[0][1])
        assert published_event["event"] == "step_start"
        assert published_event["data"]["step_id"] == f"{step_name}_{task_id}"
        assert published_event["data"]["step_name"] == step_name
        assert published_event["data"]["title"] == title
        assert published_event["data"]["message"] == message
        
        # 验证hset被调用两次（hget查询 + hset更新）
        assert mock_redis.client.hset.call_count == 1
        
        # 验证状态更新
        hset_call_args = mock_redis.client.hset.call_args
        mapping = hset_call_args[1]["mapping"]
        assert mapping["overall_status"] == "running"
        assert mapping["current_step"] == step_name
        
        # 验证steps_status更新
        steps_status = json.loads(mapping["steps_status"])
        assert step_name in steps_status
        assert steps_status[step_name]["status"] == "running"
        assert steps_status[step_name]["title"] == title
        assert steps_status[step_name]["message"] == message

    @pytest.mark.asyncio
    async def test_notify_step_end_success(self, notification_service, mock_redis):
        """测试步骤结束通知（成功）"""
        task_id = "test_task_123"
        step_name = "core_intent_analysis"
        status = "success"
        result = {"destinations": ["上海"], "days": 3}
        
        # 模拟已存在的步骤状态
        existing_steps = {
            step_name: {
                "status": "running",
                "start_time": "2023-01-01T10:00:00",
                "title": "解析用户需求",
                "message": "正在分析..."
            }
        }
        mock_redis.client.hget.return_value = json.dumps(existing_steps)
        
        await notification_service.notify_step_end(task_id, step_name, status, result)
        
        # 验证publish被调用
        mock_redis.client.publish.assert_called_once()
        call_args = mock_redis.client.publish.call_args
        
        # 验证发布的事件格式
        published_event = json.loads(call_args[0][1])
        assert published_event["event"] == "step_end"
        assert published_event["data"]["step_id"] == f"{step_name}_{task_id}"
        assert published_event["data"]["step_name"] == step_name
        assert published_event["data"]["status"] == status
        assert published_event["data"]["result"] == result
        
        # 验证步骤状态更新
        hset_call_args = mock_redis.client.hset.call_args
        mapping = hset_call_args[1]["mapping"]
        updated_steps = json.loads(mapping["steps_status"])
        assert updated_steps[step_name]["status"] == status
        assert "end_time" in updated_steps[step_name]
        assert updated_steps[step_name]["result"] == result

    @pytest.mark.asyncio
    async def test_notify_final_result(self, notification_service, mock_redis):
        """测试最终结果通知"""
        task_id = "test_task_123"
        final_data = {"itinerary": {"title": "上海3日游", "days": 3}}
        
        await notification_service.notify_final_result(task_id, final_data)
        
        # 验证publish被调用两次（complete + eos）
        assert mock_redis.client.publish.call_count == 2
        
        # 验证第一个事件是complete
        first_call = mock_redis.client.publish.call_args_list[0]
        complete_event = json.loads(first_call[0][1])
        assert complete_event["event"] == "complete"
        assert complete_event["data"] == final_data
        
        # 验证第二个事件是eos
        second_call = mock_redis.client.publish.call_args_list[1]
        eos_event = json.loads(second_call[0][1])
        assert eos_event["event"] == "eos"
        
        # 验证最终状态更新
        hset_call_args = mock_redis.client.hset.call_args
        mapping = hset_call_args[1]["mapping"]
        assert mapping["overall_status"] == "completed"
        assert mapping["current_step"] == "finished"
        assert json.loads(mapping["final_result"]) == final_data

    @pytest.mark.asyncio
    async def test_notify_error(self, notification_service, mock_redis):
        """测试错误通知"""
        task_id = "test_task_123"
        error_message = "LLM服务超时"
        step_name = "core_intent_analysis"
        
        await notification_service.notify_error(task_id, error_message, step_name)
        
        # 验证publish被调用两次（error + eos）
        assert mock_redis.client.publish.call_count == 2
        
        # 验证第一个事件是error
        first_call = mock_redis.client.publish.call_args_list[0]
        error_event = json.loads(first_call[0][1])
        assert error_event["event"] == "error"
        assert error_event["data"]["step_name"] == step_name
        assert error_event["data"]["message"] == error_message
        
        # 验证第二个事件是eos
        second_call = mock_redis.client.publish.call_args_list[1]
        eos_event = json.loads(second_call[0][1])
        assert eos_event["event"] == "eos"
        
        # 验证错误状态更新
        hset_call_args = mock_redis.client.hset.call_args
        mapping = hset_call_args[1]["mapping"]
        assert mapping["overall_status"] == "failed"
        assert mapping["current_step"] == step_name
        assert mapping["error_info"] == error_message

    @pytest.mark.asyncio
    async def test_get_task_status_exists(self, notification_service, mock_redis):
        """测试获取存在的任务状态"""
        task_id = "test_task_123"
        
        # 模拟Redis返回的数据
        mock_data = {
            "overall_status": "running",
            "current_step": "core_intent_analysis",
            "steps_status": '{"core_intent_analysis": {"status": "running"}}',
            "final_result": "",
            "last_updated": "2023-01-01T10:00:00"
        }
        mock_redis.client.hgetall.return_value = mock_data
        
        result = await notification_service.get_task_status(task_id)
        
        # 验证hgetall被调用
        mock_redis.client.hgetall.assert_called_once_with(f"task_status:{task_id}")
        
        # 验证返回结果
        assert result is not None
        assert result["overall_status"] == "running"
        assert result["current_step"] == "core_intent_analysis"
        assert isinstance(result["steps_status"], dict)
        assert result["final_result"] is None  # 空字符串转为None
        assert result["last_updated"] == "2023-01-01T10:00:00"

    @pytest.mark.asyncio
    async def test_get_task_status_not_exists(self, notification_service, mock_redis):
        """测试获取不存在的任务状态"""
        task_id = "test_task_123"
        
        # 模拟Redis返回空数据
        mock_redis.client.hgetall.return_value = {}
        
        result = await notification_service.get_task_status(task_id)
        
        # 验证返回None
        assert result is None

    @pytest.mark.asyncio
    async def test_cleanup_task_success(self, notification_service, mock_redis):
        """测试成功清理任务"""
        task_id = "test_task_123"
        
        # 模拟删除成功
        mock_redis.client.delete.return_value = 1
        
        result = await notification_service.cleanup_task(task_id)
        
        # 验证delete被调用
        mock_redis.client.delete.assert_called_once_with(f"task_status:{task_id}")
        
        # 验证返回True
        assert result is True

    @pytest.mark.asyncio
    async def test_cleanup_task_failure(self, notification_service, mock_redis):
        """测试清理任务失败"""
        task_id = "test_task_123"
        
        # 模拟删除时抛出异常
        mock_redis.client.delete.side_effect = Exception("Redis连接失败")
        
        result = await notification_service.cleanup_task(task_id)
        
        # 验证返回False
        assert result is False

    @pytest.mark.asyncio
    async def test_serialization_handling(self, notification_service, mock_redis):
        """测试数据序列化处理"""
        task_id = "test_task_123"
        
        # 测试包含复杂数据的更新
        updates = {
            "dict_field": {"key": "value"},
            "list_field": [1, 2, 3],
            "none_field": None,
            "string_field": "test"
        }
        
        await notification_service._update_task_status(task_id, updates)
        
        # 验证序列化处理
        hset_call_args = mock_redis.client.hset.call_args
        mapping = hset_call_args[1]["mapping"]
        
        # 字典和列表应该被JSON序列化
        assert json.loads(mapping["dict_field"]) == {"key": "value"}
        assert json.loads(mapping["list_field"]) == [1, 2, 3]
        
        # None应该转为空字符串
        assert mapping["none_field"] == ""
        
        # 字符串保持不变
        assert mapping["string_field"] == "test"
        
        # 应该包含last_updated时间戳
        assert "last_updated" in mapping

    @pytest.mark.asyncio
    async def test_redis_error_handling(self, notification_service, mock_redis):
        """测试Redis错误处理"""
        task_id = "test_task_123"
        
        # 模拟Redis操作失败
        mock_redis.client.publish.side_effect = Exception("Redis连接失败")
        
        # 这里我们只是确保方法不会崩溃
        # 实际的错误处理逻辑可能需要根据具体需求调整
        try:
            await notification_service.notify_step_start(
                task_id, "test_step", "测试步骤", "测试消息"
            )
        except Exception:
            # 预期可能会有异常，这里我们测试异常是否被适当处理
            pass 