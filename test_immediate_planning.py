"""
测试立即规划功能
验证完整的规划流程和结果质量
"""

import asyncio
import json
import httpx
import time
from typing import List, Dict, Any

# API配置
BASE_URL = "http://localhost:8000"
HEADERS = {"Content-Type": "application/json"}

class ImmediatePlanningTester:
    def __init__(self):
        self.base_url = BASE_URL
        self.headers = HEADERS
    
    async def test_immediate_planning(self, query: str, user_id: str, test_name: str):
        """
        测试立即规划功能
        
        Args:
            query: 用户查询
            user_id: 用户ID
            test_name: 测试名称
        """
        print(f"🚀 {test_name}")
        print("=" * 60)
        print(f"📝 查询: {query}")
        print(f"👤 用户: {user_id}")
        
        # 使用V2 SSE接口
        url = f"{self.base_url}/api/travel/v2/plan/stream"
        
        request_data = {
            "query": query,
            "user_id": user_id
        }
        
        print(f"\n🌐 请求URL: {url}")
        print("\n📡 SSE事件流:")
        print("-" * 40)
        
        events = []
        step_times = {}
        final_result = None
        
        try:
            start_time = time.time()
            
            async with httpx.AsyncClient(timeout=180.0) as client:
                async with client.stream("POST", url, json=request_data, headers=self.headers) as response:
                    if response.status_code != 200:
                        print(f"❌ HTTP错误: {response.status_code}")
                        return events, None
                    
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data_str = line[6:]  # 移除 "data: " 前缀
                            
                            if data_str.strip() == "[DONE]":
                                print("✅ 流式传输完成")
                                break
                            
                            try:
                                event_data = json.loads(data_str)
                                events.append(event_data)
                                
                                # 解析事件信息
                                event_type = event_data.get("event", "unknown")
                                data = event_data.get("data", {})
                                step_name = data.get("step_name", "")
                                
                                if event_type == "step_start":
                                    step_times[step_name] = {"start": time.time()}
                                    print(f"🚀 开始: {step_name} - {data.get('title', '')}")
                                
                                elif event_type == "step_end":
                                    if step_name in step_times:
                                        step_times[step_name]["end"] = time.time()
                                        duration = step_times[step_name]["end"] - step_times[step_name]["start"]
                                        step_times[step_name]["duration"] = duration
                                    else:
                                        duration = 0
                                    
                                    status = data.get("status", "unknown")
                                    result = data.get("result", {})
                                    
                                    print(f"✅ 完成: {step_name} - {status} ({duration:.2f}秒)")
                                    
                                    # 显示重要结果
                                    if "content" in result:
                                        content = result["content"]
                                        print(f"   📋 结果: {content}")
                                    
                                    # 特别处理规划执行结果
                                    if step_name == "planning_execution":
                                        if "city_plan_results" in result:
                                            city_plans = result["city_plan_results"]
                                            print(f"   🏙️ 城市规划: {len(city_plans)}个城市")
                                            for plan in city_plans:
                                                city = plan.get("city", "N/A")
                                                attractions = len(plan.get("planned_attractions", []))
                                                foods = len(plan.get("planned_restaurants", []))
                                                print(f"      {city}: {attractions}个景点, {foods}个餐厅")
                                        
                                        if "driving_routes" in result:
                                            routes = result["driving_routes"]
                                            print(f"   🚗 路线规划: {len(routes)}条路线")
                                            for route in routes:
                                                origin = route.get("origin", "N/A")
                                                dest = route.get("destination", "N/A")
                                                distance = route.get("distance", "N/A")
                                                duration = route.get("duration", "N/A")
                                                print(f"      {origin} -> {dest}: {distance}, {duration}")
                                
                                elif event_type == "final_result":
                                    final_result = data
                                    print(f"🎯 最终结果已生成")
                                        
                            except json.JSONDecodeError as e:
                                print(f"❌ JSON解析错误: {e}")
                                print(f"原始数据: {line}")
        
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
        
        total_time = time.time() - start_time
        print(f"\n📊 总耗时: {total_time:.2f}秒")
        print(f"📊 总事件数: {len(events)}")
        
        return events, step_times, final_result
    
    def analyze_planning_quality(self, events: List[Dict], step_times: Dict, final_result: Dict, test_name: str):
        """分析规划质量"""
        print(f"\n📊 {test_name} - 质量分析")
        print("=" * 60)
        
        # 分析步骤执行时间
        print("⏱️ 步骤执行时间:")
        for step_name, times in step_times.items():
            duration = times.get("duration", 0)
            if duration < 0.5:
                status = "⚡ 可能硬编码"
            elif duration > 1:
                status = "🤖 可能使用LLM"
            else:
                status = "⚖️ 中等时间"
            
            print(f"   {step_name}: {duration:.2f}秒 - {status}")
        
        # 分析LLM使用率
        llm_steps = sum(1 for times in step_times.values() if times.get("duration", 0) >= 1)
        total_steps = len(step_times)
        llm_rate = (llm_steps / total_steps * 100) if total_steps > 0 else 0
        
        print(f"\n📈 LLM使用率: {llm_rate:.1f}% ({llm_steps}/{total_steps})")
        
        # 检查硬编码问题
        hardcoded_steps = [
            step for step, times in step_times.items() 
            if times.get("duration", 0) < 0.5
        ]
        
        if hardcoded_steps:
            print(f"⚠️ 可能硬编码的步骤: {', '.join(hardcoded_steps)}")
        else:
            print("✅ 未发现明显的硬编码问题")
        
        # 分析最终结果质量
        if final_result:
            print(f"\n🎯 最终结果质量:")
            print(f"   ✅ 生成了最终结果")
        else:
            print(f"\n❌ 未生成最终结果")

async def main():
    """主测试函数"""
    print("🧪 立即规划功能完整测试")
    print("验证规划流程的完整性和质量\n")
    
    tester = ImmediatePlanningTester()
    
    # 测试用例1: 单城市规划
    print("🔍 测试用例1: 单城市规划")
    events1, times1, result1 = await tester.test_immediate_planning(
        "我想去北京旅游3天，喜欢历史文化和传统美食，预算中等",
        "test_immediate_1",
        "单城市规划测试"
    )
    tester.analyze_planning_quality(events1, times1, result1, "单城市规划")
    
    # 等待一下避免请求冲突
    await asyncio.sleep(3)
    
    # 测试用例2: 多城市规划
    print("\n" + "="*80)
    print("🔍 测试用例2: 多城市规划")
    events2, times2, result2 = await tester.test_immediate_planning(
        "我想去北京和上海旅游5天，先去北京2天看历史文化，再去上海3天体验现代都市",
        "test_immediate_2", 
        "多城市规划测试"
    )
    tester.analyze_planning_quality(events2, times2, result2, "多城市规划")
    
    # 总结对比
    print("\n" + "="*80)
    print("📊 测试总结对比")
    print("="*80)
    
    test_cases = [
        ("单城市规划", times1),
        ("多城市规划", times2)
    ]
    
    for name, times in test_cases:
        if times:
            total_duration = sum(t.get("duration", 0) for t in times.values())
            llm_steps = sum(1 for t in times.values() if t.get("duration", 0) >= 1)
            total_steps = len(times)
            llm_rate = (llm_steps / total_steps * 100) if total_steps > 0 else 0
            
            print(f"\n{name}:")
            print(f"  总耗时: {total_duration:.2f}秒")
            print(f"  LLM使用率: {llm_rate:.1f}%")
            print(f"  步骤数: {total_steps}")
        else:
            print(f"\n{name}: 测试失败")

if __name__ == "__main__":
    asyncio.run(main())
