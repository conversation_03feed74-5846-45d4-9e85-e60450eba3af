/**
 * AutoPilot AI - 重构版前端应用
 * 
 * 支持两阶段交互模式和TTS播报的现代化界面
 */

class TravelPlannerAppRefactored {
    constructor() {
        this.currentTraceId = null;
        this.eventSource = null;
        this.currentItinerary = null;
        this.currentPhase = 'waiting'; // waiting, analysis, planning, completed
        this.currentUserId = null;
        this.currentQuery = null;

        // 请求锁机制 - 根据推送.md文档要求
        this.isPlanning = false;

        // 动态步骤管理 - 完全由后端驱动
        this.dynamicSteps = new Map(); // step_id -> step_info
        this.stepOrder = []; // 保存步骤顺序

        // 分析阶段步骤跟踪 - 四个必需的分析步骤
        this.analysisStepsStatus = {
            'core_intent_analysis': false,      // 意图分析
            'food_preferences_analysis': false,  // 美食偏好分析
            'attraction_preferences_analysis': false, // 景点偏好分析
            'accommodation_preferences_analysis': false // 住宿偏好分析
        };

        // 状态数据存储
        this.state = {
            core_intent: null,
            user_profile: null,
            user_memories: null,
            travel_preferences: null,
            preference_profile: null,
            driving_context: null,
            vehicle_info: null,
            multi_city_strategy: null
        };

        this.init();
    }
    
    init() {
        this.bindEvents();
        this.setupViewModes();
        this.loadUserHistory();
        this.updateUI();
    }
    
    bindEvents() {
        // 规划表单提交
        document.getElementById('planningForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.startPlanning();
        });
        
        // 视图模式切换
        document.getElementById('viewModeList').addEventListener('click', () => {
            this.switchViewMode('list');
        });
        
        document.getElementById('viewModeMap').addEventListener('click', () => {
            this.switchViewMode('map');
        });
        
        // 立即规划按钮
        const startPlanningBtn = document.getElementById('startPlanningBtn');
        if (startPlanningBtn) {
            startPlanningBtn.addEventListener('click', () => {
                this.startItineraryPlanning();
            });
        }
        
        // 取消规划按钮
        const cancelPlanningBtn = document.getElementById('cancelPlanningBtn');
        if (cancelPlanningBtn) {
            cancelPlanningBtn.addEventListener('click', () => {
                this.cancelPlanning();
            });
        }
        
        // 行程操作按钮
        document.getElementById('saveItinerary').addEventListener('click', () => {
            this.saveItinerary();
        });
        
        document.getElementById('editItinerary').addEventListener('click', () => {
            this.editItinerary();
        });
        
        document.getElementById('shareItinerary').addEventListener('click', () => {
            this.shareItinerary();
        });
        
        // 历史行程按钮
        document.getElementById('historyBtn').addEventListener('click', () => {
            this.showHistory();
        });
    }
    
    setupViewModes() {
        // 默认显示列表视图
        this.switchViewMode('list');
    }
    
    updateUI() {
        // 根据当前阶段更新UI显示
        this.hideAllViews();

        switch (this.currentPhase) {
            case 'waiting':
                this.showWaitingView();
                break;
            case 'analysis':
                this.showAnalysisView();
                break;
            case 'planning':
                this.showPlanningView();
                break;
            case 'completed':
                this.showCompletedView();
                break;
        }
    }

    hideAllViews() {
        document.getElementById('waitingView').style.display = 'none';
        document.getElementById('analysisView').style.display = 'none';
        document.getElementById('itineraryView').style.display = 'none';
    }

    showWaitingView() {
        document.getElementById('waitingView').style.display = 'flex';
    }

    showAnalysisView() {
        document.getElementById('analysisView').style.display = 'flex';

        // 更新分析状态文本
        const title = document.getElementById('analysisStatusTitle');
        const desc = document.getElementById('analysisStatusDesc');

        if (title) title.textContent = '正在分析您的需求...';
        if (desc) desc.textContent = 'AI正在理解您的旅行偏好和需求';
    }

    showPlanningView() {
        // 规划阶段：左侧保留意图理解，右侧显示规划过程
        // 隐藏等待和分析状态视图
        document.getElementById('waitingView').style.display = 'none';
        document.getElementById('analysisView').style.display = 'none';

        // 显示规划视图（在右侧面板中）
        document.getElementById('itineraryView').style.display = 'block';

        // 确保左侧分析面板保持可见（显示已完成的意图理解结果）
        const analysisPanel = document.querySelector('.analysis-panel');
        if (analysisPanel) {
            analysisPanel.style.display = 'block';
        }

        // 在右侧显示规划状态
        this.showPlanningStatus();
    }

    showPlanningStatus() {
        // 在右侧面板显示规划状态
        const itineraryView = document.getElementById('itineraryView');
        if (itineraryView) {
            itineraryView.innerHTML = `
                <div class="planning-status-content">
                    <div class="status-content">
                        <div class="status-spinner">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                        <h4 class="status-title">🚀 开始规划您的精彩行程...</h4>
                        <p class="status-description">AI正在为您生成个性化的旅行方案</p>
                        <div class="planning-progress">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%" id="planningProgressBar"></div>
                            </div>
                            <div class="progress-text mt-2" id="planningProgressText">准备开始...</div>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    showCompletedView() {
        document.getElementById('itineraryView').style.display = 'block';
    }

    // 添加缺失的方法
    regenerateItinerary() {
        if (confirm('确定要重新生成行程吗？')) {
            // 重新启动规划阶段
            this.startPlanningPhase();
        }
    }

    viewItinerary() {
        // 切换到行程查看模式
        this.currentPhase = 'completed';
        this.updateUI();

        // TTS播报
        if (window.ttsManager) {
            window.ttsManager.speakStatusUpdate('为您展示完整的旅行行程');
        }
    }
    
    async startPlanning() {
        // 1. 加锁检查 - 根据推送.md文档要求
        if (this.isPlanning) {
            console.log("已有规划任务在进行中，请勿重复点击。");
            return;
        }

        const query = document.getElementById('userQuery').value.trim();
        const userId = document.getElementById('userId').value.trim() || '1';

        if (!query) {
            this.showAlert('请输入您的旅行想法', 'warning');
            return;
        }

        try {
            // 2. 加锁
            this.isPlanning = true;
            const planButton = document.getElementById('planningForm').querySelector('button[type="submit"]');
            if (planButton) {
                planButton.disabled = true;
                planButton.innerHTML = '<i class="bi bi-hourglass-split"></i> 分析中...';
            }

            // 3. 清理旧的UI状态
            this.clearPreviousResults();

            // 保存当前查询和用户ID
            this.currentQuery = query;
            this.currentUserId = userId;

            // 切换到分析阶段
            this.currentPhase = 'analysis';
            this.updateUI();

            // TTS播报
            if (window.ttsManager) {
                window.ttsManager.speakStatusUpdate('开始分析您的旅行需求');
            }

            // 重置动态步骤状态
            this.clearPreviousResults();

            // 4. 开始真实的SSE连接
            await this.startRealPlanning(query, userId);

        } catch (error) {
            console.error('规划失败:', error);
            this.showAlert('规划失败: ' + error.message, 'danger');

            // 出现错误时解锁
            this.unlockPlanning();
        }
    }

    clearPreviousResults() {
        // 清理之前的结果
        this.dynamicSteps.clear();
        this.stepOrder = [];
        this.currentItinerary = null;

        // 重置分析步骤状态
        Object.keys(this.analysisStepsStatus).forEach(step => {
            this.analysisStepsStatus[step] = false;
        });

        // 重置状态数据
        Object.keys(this.state).forEach(key => {
            this.state[key] = null;
        });

        // 隐藏立即规划按钮
        this.hideStartPlanningButton();

        // 清理动态创建的步骤UI
        const stepsContainer = document.getElementById('analysisSteps');
        if (stepsContainer) {
            stepsContainer.innerHTML = `
                <div class="analysis-placeholder">
                    <div class="text-center text-muted">
                        <i class="bi bi-clock-history"></i>
                        <p class="mt-2">等待开始分析...</p>
                    </div>
                </div>
            `;
        }

        // 清理右侧动态步骤容器
        const dynamicContainer = document.getElementById('dynamicStepsContainer');
        if (dynamicContainer) {
            dynamicContainer.innerHTML = '';
        }
    }

    unlockPlanning() {
        // 解锁规划状态
        this.isPlanning = false;
        const planButton = document.getElementById('planningForm').querySelector('button[type="submit"]');
        if (planButton) {
            planButton.disabled = false;
            planButton.innerHTML = '<i class="bi bi-search"></i> 开始规划';
        }
    }

    unlockForNextPhase() {
        // 为下一阶段解锁（分析完成，准备规划）
        this.isPlanning = false;
        const planButton = document.getElementById('planningForm').querySelector('button[type="submit"]');
        if (planButton) {
            planButton.disabled = false;
            planButton.innerHTML = '<i class="bi bi-check-circle"></i> 分析完成';
            planButton.style.backgroundColor = '#28a745';
            planButton.style.borderColor = '#28a745';
        }
    }
    

    
    // 模拟分析过程已移除，现在使用真实的SSE连接
    
    setAnalysisStepActive(stepKey) {
        const stepElement = document.querySelector(`[data-step="${stepKey}"]`);
        if (stepElement) {
            stepElement.classList.add('active');
            stepElement.classList.remove('completed');
        }
    }
    
    completeAnalysisStep(stepKey, content) {
        this.analysisSteps[stepKey].completed = true;
        
        const stepElement = document.querySelector(`[data-step="${stepKey}"]`);
        if (stepElement) {
            stepElement.classList.remove('active');
            stepElement.classList.add('completed');
            
            // 更新结果显示
            const resultElement = stepElement.querySelector('.analysis-result');
            if (resultElement) {
                resultElement.innerHTML = `<div class="analysis-content-text">${content}</div>`;
            }
            
            // 更新状态图标
            const statusElement = stepElement.querySelector('.analysis-status');
            if (statusElement) {
                statusElement.innerHTML = '<i class="bi bi-check-circle-fill"></i>';
            }
        }
    }
    
    // ==================== 动态UI管理方法 ====================

    showStepInProgress(stepId, title, message) {
        console.log('显示进行中的步骤:', stepId, title, message);

        // 确保动态步骤容器存在
        let container = document.getElementById('dynamicStepsContainer');
        if (!container) {
            console.warn('动态步骤容器不存在');
            return;
        }

        // 检查步骤卡片是否已存在
        let stepCard = document.getElementById(`step-card-${stepId}`);
        if (!stepCard) {
            // 创建新的步骤卡片
            stepCard = document.createElement('div');
            stepCard.id = `step-card-${stepId}`;
            stepCard.className = 'dynamic-step-card running';
            stepCard.innerHTML = `
                <div class="dynamic-step-icon">
                    <i class="bi bi-gear"></i>
                </div>
                <div class="dynamic-step-content">
                    <div class="dynamic-step-title">${title}</div>
                    <div class="dynamic-step-message">${message}</div>
                    </div>
            `;
            container.appendChild(stepCard);
        } else {
            // 更新现有卡片
            stepCard.className = 'dynamic-step-card running';
            stepCard.querySelector('.dynamic-step-title').textContent = title;
            stepCard.querySelector('.dynamic-step-message').textContent = message;
        }

        // 滚动到步骤卡片
        stepCard.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }

    showStepSuccess(stepId, result) {
        console.log('显示成功的步骤:', stepId, result);

        const stepCard = document.getElementById(`step-card-${stepId}`);
        if (!stepCard) {
            console.warn('找不到步骤卡片:', stepId);
            return;
        }

        // 更新为成功状态
        stepCard.className = 'dynamic-step-card success';
        
        // 更新图标
        const icon = stepCard.querySelector('.dynamic-step-icon i');
        if (icon) {
            icon.className = 'bi bi-check-circle';
        }

        // 更新消息
        const messageEl = stepCard.querySelector('.dynamic-step-message');
        if (messageEl) {
            messageEl.textContent = '步骤完成';
        }

        // 如果有结果数据，显示结果
        if (result && (result.content || result.destinations || result.strategy)) {
            let resultContainer = stepCard.querySelector('.dynamic-step-result');
            if (!resultContainer) {
                resultContainer = document.createElement('div');
                resultContainer.className = 'dynamic-step-result';
                stepCard.querySelector('.dynamic-step-content').appendChild(resultContainer);
        }

            let resultText = '';
            if (result.content) {
                resultText = result.content;
            } else if (result.destinations) {
                resultText = `识别目的地：${result.destinations.join(', ')}`;
            } else if (result.strategy) {
                resultText = `策略：${result.strategy.order ? result.strategy.order.join(' → ') : '已制定'}`;
            } else {
                resultText = JSON.stringify(result);
            }

            resultContainer.textContent = resultText;
        }
    }

    showStepError(stepId, errorMessage) {
        console.log('显示错误的步骤:', stepId, errorMessage);

        const stepCard = document.getElementById(`step-card-${stepId}`);
        if (!stepCard) {
            console.warn('找不到步骤卡片:', stepId);
            return;
        }

        // 更新为错误状态
        stepCard.className = 'dynamic-step-card error';
        
        // 更新图标
        const icon = stepCard.querySelector('.dynamic-step-icon i');
        if (icon) {
            icon.className = 'bi bi-exclamation-triangle';
        }

        // 更新消息
        const messageEl = stepCard.querySelector('.dynamic-step-message');
        if (messageEl) {
            messageEl.textContent = errorMessage || '步骤执行失败';
        }
    }

    formatAttractionPreferences(result) {
        // 格式化景点偏好分析结果
        const parts = [];

        // 提取主要偏好类型
        if (result.attraction_type_preferences) {
            const prefs = result.attraction_type_preferences;
            const topPrefs = [];

            // 文化历史类
            if (prefs.cultural_historical) {
                const cultural = Object.entries(prefs.cultural_historical)
                    .filter(([key, value]) => value >= 7)
                    .map(([key, value]) => this.translateAttractionType(key));
                if (cultural.length > 0) topPrefs.push(...cultural);
            }

            // 自然风光类
            if (prefs.natural_scenery) {
                const natural = Object.entries(prefs.natural_scenery)
                    .filter(([key, value]) => value >= 7)
                    .map(([key, value]) => this.translateAttractionType(key));
                if (natural.length > 0) topPrefs.push(...natural);
            }

            // 特色体验类
            if (prefs.special_experiences) {
                const special = Object.entries(prefs.special_experiences)
                    .filter(([key, value]) => value >= 7)
                    .map(([key, value]) => this.translateAttractionType(key));
                if (special.length > 0) topPrefs.push(...special);
            }

            if (topPrefs.length > 0) {
                parts.push(`偏好类型：${topPrefs.join('、')}`);
            }
        }

        // 游览风格
        if (result.touring_style && result.touring_style.primary_style) {
            parts.push(`游览风格：${result.touring_style.primary_style}`);
        }

        // 如果没有明确偏好，显示推荐备注
        if (parts.length === 0 && result.recommendation_notes) {
            return result.recommendation_notes;
        }

        return parts.length > 0 ? parts.join(' | ') : '基于用户画像的个性化景点偏好分析';
    }

    formatFoodPreferences(result) {
        // 格式化美食偏好分析结果
        const parts = [];

        // 检查是否有明确的偏好数据
        const hasValidData = this.hasValidFoodPreferences(result);

        if (!hasValidData) {
            return '暂无明确美食偏好数据，将基于目的地特色推荐';
        }

        // 口味偏好
        if (result.taste_preferences && result.taste_preferences.basic_tastes) {
            const tastes = Object.entries(result.taste_preferences.basic_tastes)
                .filter(([key, value]) => value !== '暂无明确偏好数据' && value)
                .map(([key, value]) => this.translateTasteType(key));
            if (tastes.length > 0) {
                parts.push(`口味偏好：${tastes.join('、')}`);
            }
        }

        // 菜系偏好
        if (result.cuisine_preferences && result.cuisine_preferences.chinese_cuisines) {
            const cuisines = Object.entries(result.cuisine_preferences.chinese_cuisines)
                .filter(([key, value]) => value !== '暂无明确偏好数据' && value)
                .map(([key, value]) => this.translateCuisineType(key));
            if (cuisines.length > 0) {
                parts.push(`菜系偏好：${cuisines.join('、')}`);
            }
        }

        return parts.length > 0 ? parts.join(' | ') : '基于用户画像的个性化美食偏好分析';
    }

    formatAccommodationPreferences(result) {
        // 格式化住宿偏好分析结果
        const parts = [];

        // 预算范围
        if (result.budget_analysis && result.budget_analysis.budget_range) {
            parts.push(`预算：${result.budget_analysis.budget_range}`);
        }

        // 住宿类型偏好
        if (result.accommodation_type_preferences) {
            const topTypes = Object.entries(result.accommodation_type_preferences)
                .filter(([key, value]) => value >= 7)
                .map(([key, value]) => this.translateAccommodationType(key));
            if (topTypes.length > 0) {
                parts.push(`类型偏好：${topTypes.join('、')}`);
            }
        }

        // 必备设施
        if (result.filtering_criteria && result.filtering_criteria.must_have_features) {
            const features = result.filtering_criteria.must_have_features;
            if (features.length > 0) {
                parts.push(`必备设施：${features.join('、')}`);
            }
        }

        return parts.length > 0 ? parts.join(' | ') : '基于用户画像的个性化住宿偏好分析';
    }

    // 翻译方法
    translateAttractionType(key) {
        const translations = {
            'ancient_architecture': '古建筑',
            'museums': '博物馆',
            'cultural_districts': '文化街区',
            'religious_sites': '宗教场所',
            'mountains': '山景',
            'water_features': '水景',
            'gardens_parks': '园林公园',
            'nature_reserves': '自然保护区',
            'folk_culture': '民俗文化',
            'food_exploration': '美食探索',
            'outdoor_activities': '户外活动',
            'photography_spots': '摄影景点'
        };
        return translations[key] || key;
    }

    translateTasteType(key) {
        const translations = {
            'sweet': '甜味',
            'sour': '酸味',
            'spicy': '辣味',
            'salty': '咸味',
            'umami': '鲜味',
            'bitter': '苦味'
        };
        return translations[key] || key;
    }

    translateCuisineType(key) {
        const translations = {
            'sichuan': '川菜',
            'cantonese': '粤菜',
            'hunan': '湘菜',
            'shandong': '鲁菜',
            'jiangsu': '苏菜',
            'zhejiang': '浙菜',
            'fujian': '闽菜',
            'anhui': '徽菜'
        };
        return translations[key] || key;
    }

    translateAccommodationType(key) {
        const translations = {
            'luxury_hotels': '豪华酒店',
            'boutique_hotels': '精品酒店',
            'business_hotels': '商务酒店',
            'budget_hotels': '经济酒店',
            'guesthouses': '民宿',
            'hostels': '青年旅社'
        };
        return translations[key] || key;
    }

    hasValidFoodPreferences(result) {
        // 检查是否有有效的美食偏好数据（不是"暂无明确偏好数据"）
        if (!result.taste_preferences && !result.cuisine_preferences) {
            return false;
        }

        // 检查基本口味偏好
        if (result.taste_preferences && result.taste_preferences.basic_tastes) {
            const hasValidTastes = Object.values(result.taste_preferences.basic_tastes)
                .some(value => value !== '暂无明确偏好数据' && value);
            if (hasValidTastes) return true;
        }

        // 检查菜系偏好
        if (result.cuisine_preferences && result.cuisine_preferences.chinese_cuisines) {
            const hasValidCuisines = Object.values(result.cuisine_preferences.chinese_cuisines)
                .some(value => value !== '暂无明确偏好数据' && value);
            if (hasValidCuisines) return true;
        }

        return false;
    }

    updateStepProgress(stepId, progress, message) {
        console.log('更新步骤进度:', stepId, progress, message);

        const messageElement = document.getElementById(`message-${stepId}`);
        if (messageElement) {
            messageElement.innerHTML = `${message} (${progress}%)`;
        }
    }

    formatStepResult(result) {
        // 格式化步骤结果为显示文本
        if (typeof result === 'string') {
            return result;
        } else if (typeof result === 'object' && result !== null) {
            // 根据结果类型进行智能格式化
            if (result.destinations && result.days) {
                // 核心意图分析结果
                const destinations = Array.isArray(result.destinations) ? result.destinations.join('、') : result.destinations;
                const parts = [`${destinations} | ${result.days}天`];

                if (result.travel_theme) parts.push(result.travel_theme);

                return parts.join(' | ');
            } else if (result.attraction_type_preferences) {
                // 景点偏好分析结果 - 新的真实LLM结果格式
                return this.formatAttractionPreferences(result);
            } else if (result.taste_preferences && result.cuisine_preferences) {
                // 美食偏好分析结果 - 新的真实LLM结果格式
                return this.formatFoodPreferences(result);
            } else if (result.accommodation_type_preferences) {
                // 住宿偏好分析结果 - 新的真实LLM结果格式
                return this.formatAccommodationPreferences(result);
            } else if (result.preferred_types) {
                // 旧的景点偏好结果格式（向后兼容）
                return `景点推荐：${result.preferred_types.join('、')}`;
            } else if (result.taste_preferences || result.cuisine_preferences) {
                // 旧的美食偏好结果格式（向后兼容）
                const parts = [];
                if (result.taste_preferences) parts.push(`口味：${Object.keys(result.taste_preferences).join('、')}`);
                if (result.cuisine_preferences) parts.push(`菜系：${Object.keys(result.cuisine_preferences).join('、')}`);
                return parts.join(' | ');
            } else if (result.budget_range || result.special_requirements) {
                // 旧的住宿偏好结果格式（向后兼容）
                const parts = [];
                if (result.budget_range) parts.push(result.budget_range);
                if (result.special_requirements) parts.push(result.special_requirements.join('、'));
                return parts.join(' | ');
            } else {
                // 通用对象格式化
                const keys = Object.keys(result);
                if (keys.length <= 3) {
                    return keys.map(key => `${key}: ${result[key]}`).join(' | ');
                } else {
                    return `包含 ${keys.length} 项分析结果`;
                }
            }
        } else {
            return String(result);
        }
    }

    showStartPlanningButton() {
        const startBtn = document.getElementById('startPlanningBtn');
        const cancelBtn = document.getElementById('cancelPlanningBtn');

        // 重置规划状态，允许用户点击立即规划按钮
        this.isPlanning = false;

        if (startBtn) {
            startBtn.style.display = 'block';
            startBtn.disabled = false;
            startBtn.innerHTML = '<i class="bi bi-play-circle"></i> 立即规划';
            startBtn.classList.add('animate__animated', 'animate__fadeInUp');
            startBtn.onclick = () => this.startPlanningPhase();
        }

        if (cancelBtn) {
            cancelBtn.style.display = 'inline-block';
        }

        // TTS播报
        if (window.ttsManager) {
            window.ttsManager.speakStatusUpdate('分析完成，点击立即规划开始生成行程');
        }
    }

    hideStartPlanningButton() {
        const startBtn = document.getElementById('startPlanningBtn');
        if (startBtn) {
            startBtn.style.display = 'none';
        }
    }

    disableStartPlanningButton() {
        const startBtn = document.getElementById('startPlanningBtn');
        if (startBtn) {
            startBtn.disabled = true;
            startBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 规划中...';
        }
    }
    
    async startItineraryPlanning() {
        try {
            // 切换到规划阶段
            this.currentPhase = 'planning';
            this.updateUI();

            // 隐藏按钮
            document.getElementById('startPlanningBtn').style.display = 'none';

            // TTS播报
            if (window.ttsManager) {
                window.ttsManager.speakStatusUpdate('开始生成详细的旅行行程');
            }

            // 启动第二阶段的规划流程
            await this.startPlanningPhase();

        } catch (error) {
            console.error('行程规划失败:', error);
            this.showAlert('行程规划失败: ' + error.message, 'danger');
        }
    }

    async startPlanningPhase() {
        // 检查是否已经在规划中
        if (this.isPlanning) {
            console.log("规划阶段已在进行中，请勿重复点击。");
            return;
        }

        try {
            // 加锁
            this.isPlanning = true;

            // 禁用立即规划按钮
            this.disableStartPlanningButton();

            // 切换到规划阶段
            this.currentPhase = 'planning';
            this.updateUI();

            // TTS播报
            if (window.ttsManager) {
                window.ttsManager.speakStatusUpdate('开始生成详细的旅行行程');
            }

            // 准备分析结果数据
            const analysisResult = {
                core_intent: this.state.core_intent,
                user_profile: this.state.user_profile,
                food_preferences: this.state.food_preferences,
                attraction_preferences: this.state.attraction_preferences,
                accommodation_preferences: this.state.accommodation_preferences,
                user_memories: this.state.user_memories,
                travel_preferences: this.state.travel_preferences,
                driving_context: this.state.driving_context,
                vehicle_info: this.state.vehicle_info,
                multi_city_strategy: this.state.multi_city_strategy
            };

            console.log('启动规划阶段，分析结果:', analysisResult);

            // 调用规划接口
            await this.startPlanningWithAnalysis(analysisResult);

        } catch (error) {
            console.error('启动规划阶段失败:', error);
            this.showAlert('启动规划失败: ' + error.message, 'danger');

            // 出现错误时解锁
            this.unlockPlanning();
        }
    }

    async startPlanningWithAnalysis(analysisResult) {
        // 使用新的V2规划API，传递分析结果
        try {
            const response = await fetch('/api/travel/v2/plan/stream', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: this.currentUserId,
                    query: this.currentQuery,
                    analysis_result: analysisResult
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // 创建EventSource从响应流读取
            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const eventData = JSON.parse(line.substring(6));
                            this.handleSSEEvent(eventData);
                        } catch (error) {
                            console.error('解析SSE事件失败:', error, line);
                        }
                    }
                }
            }

        } catch (error) {
            console.error('V2规划API连接失败:', error);
            this.showAlert('连接服务器失败，请重试', 'danger');
            this.currentPhase = 'waiting';
            this.updateUI();
        }
    }

    async startPlanningPhaseFallback() {
        // 原有的SSE连接方式作为降级方案
        const query = document.getElementById('userQuery').value.trim();
        const userId = document.getElementById('userId').value.trim() || '1';

        const url = `/api/travel/plan/${this.currentTraceId}/stream?user_id=${userId}&query=${encodeURIComponent(query)}&phase=planning`;
        this.eventSource = new EventSource(url);

        this.eventSource.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleSSEEvent(data);
            } catch (error) {
                console.error('解析SSE事件失败:', error);
            }
        };

        this.eventSource.onerror = (error) => {
            console.error('SSE连接错误:', error);
            this.eventSource.close();
            this.showAlert('连接服务器失败，请重试', 'danger');
            this.currentPhase = 'waiting';
            this.updateUI();
        };
    }

    // 模拟行程生成已移除，现在使用真实的SSE连接
    
    displayItinerary(result) {
        console.log('显示行程数据:', result);

        // 检查数据格式
        if (!result || (!result.itinerary && !result.daily_itineraries)) {
            console.error('行程数据格式错误:', result);
            this.showAlert('行程数据格式错误', 'danger');
            return;
        }

        // 兼容不同的数据格式
        const itineraryData = result.itinerary || result.daily_itineraries || result;
        const summary = result.summary || {};

        // 更新行程标题和描述
        const title = summary.destinations?.join(' → ') || '精彩旅程';
        const description = `${summary.days || itineraryData.length}天${summary.travel_theme || '休闲'}之旅`;

        const titleElement = document.getElementById('itineraryTitle');
        const descElement = document.getElementById('itineraryDescription');

        if (titleElement) titleElement.textContent = title;
        if (descElement) descElement.textContent = description;

        // 更新统计信息
        this.updateItineraryStats(itineraryData, summary);

        // 显示详细行程卡片
        this.displayItineraryCards(itineraryData);

        // 切换到结果视图
        this.currentPhase = 'completed';
        this.updateUI();

        this.currentItinerary = result;
    }

    updateItineraryStats(itineraryData, summary) {
        // 计算统计信息
        const totalDays = itineraryData.length || summary.days || 0;
        let totalPOIs = 0;

        if (Array.isArray(itineraryData)) {
            itineraryData.forEach(day => {
                if (day.time_blocks) {
                    totalPOIs += day.time_blocks.length;
                } else if (day.activities) {
                    totalPOIs += day.activities.length;
                }
            });
        }

        // 更新DOM元素
        const totalDaysElement = document.getElementById('totalDays');
        const totalPOIsElement = document.getElementById('totalPOIs');
        const estimatedBudgetElement = document.getElementById('estimatedBudget');
        const weatherInfoElement = document.getElementById('weatherInfo');

        if (totalDaysElement) totalDaysElement.textContent = totalDays;
        if (totalPOIsElement) totalPOIsElement.textContent = totalPOIs;
        if (estimatedBudgetElement) estimatedBudgetElement.textContent = summary.estimated_budget || '待估算';
        if (weatherInfoElement) weatherInfoElement.textContent = summary.weather || '晴朗';
    }

    displayItineraryCards(itineraryData) {
        console.log('显示行程卡片:', itineraryData);

        if (!Array.isArray(itineraryData)) {
            console.error('行程数据不是数组格式:', itineraryData);
            return;
        }

        // 找到行程容器
        const container = document.querySelector('.itinerary-result') ||
                         document.querySelector('#itineraryView .result-content') ||
                         document.querySelector('#itineraryView');

        if (!container) {
            console.error('未找到行程显示容器');
            return;
        }

        // 生成行程卡片HTML
        const cardsHtml = itineraryData.map((day, index) => {
            return this.generateDayCard(day, index + 1);
        }).join('');

        // 添加操作按钮
        const actionButtonsHtml = `
            <div class="itinerary-actions mt-4">
                <button class="btn btn-primary me-2" onclick="window.app.saveItinerary()">
                    <i class="bi bi-bookmark"></i> 保存行程
                </button>
                <button class="btn btn-outline-primary me-2" onclick="window.app.editItinerary()">
                    <i class="bi bi-pencil"></i> 编辑行程
                </button>
                <button class="btn btn-outline-success" onclick="window.app.shareItinerary()">
                    <i class="bi bi-share"></i> 分享行程
                </button>
            </div>
        `;

        // 更新容器内容
        container.innerHTML = `
            <div class="itinerary-cards">
                ${cardsHtml}
            </div>
            ${actionButtonsHtml}
        `;
    }

    generateDayCard(dayData, dayNumber) {
        // 处理不同的数据格式
        const date = dayData.date || `第${dayNumber}天`;
        const activities = dayData.time_blocks || dayData.activities || [];

        // 生成活动列表
        const activitiesHtml = activities.map(activity => {
            const name = activity.location || activity.name || activity.activity || '未知活动';
            const time = activity.start_time ? `${activity.start_time} - ${activity.end_time}` : '';
            const details = activity.details || activity.description || '';

            return `
                <div class="activity-item mb-2">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="activity-info">
                            <h6 class="mb-1">${name}</h6>
                            ${details ? `<p class="text-muted small mb-0">${details}</p>` : ''}
                        </div>
                        ${time ? `<span class="badge bg-light text-dark">${time}</span>` : ''}
                    </div>
                </div>
            `;
        }).join('');

        return `
            <div class="card mb-3 day-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-calendar-day me-2"></i>
                        ${date}
                    </h5>
                </div>
                <div class="card-body">
                    ${activitiesHtml || '<p class="text-muted">暂无具体安排</p>'}
                </div>
            </div>
        `;
    }
    
    cancelPlanning() {
        // 停止当前规划
        this.currentPhase = 'waiting';
        this.updateUI();

        // 重置表单
        document.getElementById('userQuery').value = '';

        // 隐藏按钮
        document.getElementById('startPlanningBtn').style.display = 'none';
        document.getElementById('cancelPlanningBtn').style.display = 'none';

        // TTS播报
        if (window.ttsManager) {
            window.ttsManager.speakStatusUpdate('规划已取消');
        }

        this.showAlert('规划已取消', 'info');
    }

    // 行程操作方法
    saveItinerary() {
        if (!this.currentItinerary) {
            this.showAlert('没有可保存的行程', 'warning');
            return;
        }

        // 这里可以添加保存到本地存储或服务器的逻辑
        localStorage.setItem('saved_itinerary', JSON.stringify(this.currentItinerary));
        this.showAlert('行程已保存到本地', 'success');

        // TTS播报
        if (window.ttsManager) {
            window.ttsManager.speakStatusUpdate('行程已保存');
        }
    }

    editItinerary() {
        if (!this.currentItinerary) {
            this.showAlert('没有可编辑的行程', 'warning');
            return;
        }

        // 这里可以添加编辑行程的逻辑
        this.showAlert('编辑功能开发中...', 'info');
    }

    shareItinerary() {
        if (!this.currentItinerary) {
            this.showAlert('没有可分享的行程', 'warning');
            return;
        }

        // 生成分享链接或文本
        const shareText = `我的旅行计划：${this.currentItinerary.summary?.destinations?.join(' → ') || '精彩旅程'}`;

        if (navigator.share) {
            navigator.share({
                title: '我的旅行计划',
                text: shareText,
                url: window.location.href
            }).then(() => {
                this.showAlert('分享成功', 'success');
            }).catch(() => {
                this.fallbackShare(shareText);
            });
        } else {
            this.fallbackShare(shareText);
        }
    }

    fallbackShare(text) {
        // 降级分享方案：复制到剪贴板
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                this.showAlert('行程信息已复制到剪贴板', 'success');
            }).catch(() => {
                this.showAlert('分享功能暂不可用', 'warning');
            });
        } else {
            this.showAlert('分享功能暂不可用', 'warning');
        }
    }
    
    switchViewMode(mode) {
        const listBtn = document.getElementById('viewModeList');
        const mapBtn = document.getElementById('viewModeMap');
        const itineraryView = document.getElementById('itineraryView');
        const mapView = document.getElementById('mapView');
        
        if (mode === 'list') {
            listBtn.classList.add('active');
            mapBtn.classList.remove('active');
            if (itineraryView) itineraryView.style.display = 'block';
            if (mapView) mapView.style.display = 'none';
        } else {
            mapBtn.classList.add('active');
            listBtn.classList.remove('active');
            if (itineraryView) itineraryView.style.display = 'none';
            if (mapView) mapView.style.display = 'block';
        }
    }

    async startRealPlanning(query, userId) {
        // 新的V2意图识别API调用方式
        this.currentUserId = userId;
        this.currentQuery = query;

        // 使用新的V2意图识别端点
        try {
            const response = await fetch('/api/travel/v2/intent/analyze', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: userId,
                    query: query
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // 创建EventSource从响应流读取
            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const eventData = JSON.parse(line.substring(6));
                            this.handleSSEEvent(eventData);
                        } catch (error) {
                            console.error('解析SSE事件失败:', error, line);
                        }
                    }
                }
            }

        } catch (error) {
            console.error('V2意图识别API连接失败:', error);
            this.showAlert('连接服务器失败，请重试', 'danger');
            this.currentPhase = 'waiting';
            this.updateUI();
        }
    }

    handleSSEEvent(data) {
        console.log('收到V2 SSE事件:', data);

        // 处理标准化的V2事件格式
        const eventType = data.event;
        const payload = data.data || data;

        switch (eventType) {
            case 'step_start':
                this.handleStepStartV2(payload);
                break;
            case 'step_end':
                this.handleStepEndV2(payload);
                break;
            case 'complete':
                this.handleCompleteV2(payload);
                break;
            case 'error':
                this.handleErrorV2(payload);
                break;
            case 'eos':
                this.handleEOSV2();
                break;
            default:
                console.log('未知的V2事件类型:', eventType, payload);
                // 保持向后兼容，尝试旧格式处理
                this.handleLegacyFormatEvent(data.event_type, data.payload);
        }
    }

    // ==================== V2格式事件处理方法 ====================

    handleStepStartV2(payload) {
        console.log('处理V2步骤开始事件:', payload);

        const stepId = payload.step_id || payload.step_name;
        const stepName = payload.step_name;
        const title = payload.title;
        const message = payload.message;

        // 保存步骤信息
        this.dynamicSteps.set(stepId, {
            stepName: stepName,
            title: title,
            status: 'running',
            startTime: new Date().toISOString()
        });

        // 更新阶段状态
        if (this.currentPhase === 'waiting') {
            this.currentPhase = 'analysis';
            this.updateUI();
        }

        // 动态创建或更新UI元素
        this.showStepInProgress(stepId, title, message);
    }

    handleStepEndV2(payload) {
        console.log('处理V2步骤结束事件:', payload);

        const stepId = payload.step_id || payload.step_name;
        const stepName = payload.step_name;
        const status = payload.status;
        const result = payload.result;

        // 更新步骤信息
        if (this.dynamicSteps.has(stepId)) {
            const stepInfo = this.dynamicSteps.get(stepId);
            stepInfo.status = status;
            stepInfo.endTime = new Date().toISOString();
            stepInfo.result = result;
        }

        if (status === 'success') {
            this.showStepSuccess(stepId, result);

            // 更新状态数据
            if (result && result.content) {
                this.updateStateFromStepResult(stepName, result);
            }

            // 检查是否是分析阶段的步骤，并更新完成状态
            this.updateAnalysisStepStatus(stepName);

            // 检查是否所有分析步骤都已完成
            this.checkAnalysisCompletion();
        } else {
            this.showStepError(stepId, result?.message || '步骤执行失败');
            this.unlockPlanning(); // 出现错误，解锁界面
        }
    }

    handleCompleteV2(payload) {
        console.log('处理V2完成事件:', payload);

        // 检查是否是意图分析完成
        if (payload.analysis_complete) {
            console.log('意图分析完成，保存分析结果');

            // 保存分析结果到状态
            if (payload.analysis_result) {
                const analysisResult = payload.analysis_result;
                this.state.core_intent = analysisResult.core_intent;
                this.state.user_profile = analysisResult.user_profile;
                this.state.food_preferences = analysisResult.food_preferences;
                this.state.attraction_preferences = analysisResult.attraction_preferences;
                this.state.accommodation_preferences = analysisResult.accommodation_preferences;
            }

            // 显示立即规划按钮
            this.showStartPlanningButton();

            // TTS播报分析完成
            if (window.ttsManager) {
                window.ttsManager.speakStatusUpdate('意图分析完成，可以开始规划行程');
            }

            this.showAlert('意图分析完成！', 'success');
            return;
        }

        // 规划完成的处理
        this.currentPhase = 'completed';

        // 如果有最终行程数据，显示它
        if (payload.itinerary) {
            this.displayItinerary({
                data: payload.itinerary,
                summary: payload.itinerary.summary || {}
            });
        } else if (payload.final_result) {
            this.displayItinerary({
                data: payload.final_result,
                summary: payload.final_result.summary || {}
            });
        }

        this.updateUI();
        this.unlockPlanning();

        // TTS播报完成
        if (window.ttsManager) {
            window.ttsManager.speakStatusUpdate('行程规划完成！');
        }

        this.showAlert('行程规划完成！', 'success');
    }

    handleErrorV2(payload) {
        console.log('处理V2错误事件:', payload);

        const errorMessage = payload.message || '发生未知错误';
        const stepName = payload.step_name || 'unknown';

        // 显示错误信息
        this.showAlert(`规划失败: ${errorMessage}`, 'danger');
        
        // 如果有具体步骤，标记该步骤为失败
        if (stepName !== 'unknown') {
            this.showStepError(stepName, errorMessage);
        }

        // 重置状态
        this.currentPhase = 'waiting';
        this.updateUI();
        this.unlockPlanning();
    }

    handleEOSV2() {
        console.log('收到流结束信号');
        // 流结束，清理资源
        this.unlockPlanning();
    }

    // 辅助方法：根据步骤结果更新状态数据
    updateStateFromStepResult(stepName, result) {
        const content = result.content || result;

        switch (stepName) {
            case 'core_intent_analysis':
                if (result.destinations) {
                    this.state.destinations = result.destinations;
                }
                if (result.duration_days) {
                    this.state.duration_days = result.duration_days;
            }
                break;
            case 'multi_city_strategy':
                if (result.strategy) {
                    this.state.multi_city_strategy = result.strategy;
                }
                break;
            case 'driving_context':
                if (result.driving_context) {
                    this.state.driving_context = result.driving_context;
                }
                break;
            case 'preferences_analysis':
                if (result.preferences) {
                    this.state.travel_preferences = result.preferences;
            }
                break;
        }
    }

    // 更新分析步骤完成状态
    updateAnalysisStepStatus(stepName) {
        // 映射后端步骤名称到前端跟踪的步骤
        const stepMapping = {
            'core_intent_analysis': 'core_intent_analysis',
            'food_preferences_analysis': 'food_preferences_analysis',
            'attraction_preferences_analysis': 'attraction_preferences_analysis',
            'accommodation_preferences_analysis': 'accommodation_preferences_analysis',
            // 兼容可能的其他命名方式
            'food_preference_analysis': 'food_preferences_analysis',
            'attraction_preference_analysis': 'attraction_preferences_analysis',
            'accommodation_preference_analysis': 'accommodation_preferences_analysis'
        };

        const mappedStep = stepMapping[stepName];
        if (mappedStep && this.analysisStepsStatus.hasOwnProperty(mappedStep)) {
            this.analysisStepsStatus[mappedStep] = true;
            console.log(`分析步骤完成: ${mappedStep}`, this.analysisStepsStatus);

            // 每完成一个步骤，检查是否所有步骤都完成了
            this.checkAnalysisCompletion();
        }
    }

    // 检查所有分析步骤是否完成
    checkAnalysisCompletion() {
        const allStepsCompleted = Object.values(this.analysisStepsStatus).every(status => status === true);

        console.log('检查分析完成状态:', this.analysisStepsStatus, '全部完成:', allStepsCompleted);

        if (allStepsCompleted && this.currentPhase === 'analysis') {
            console.log('所有分析步骤已完成，显示立即规划按钮');
            // 注意：不在这里显示按钮，而是等待后端发送complete事件
            // this.showStartPlanningButton();
        }
    }

    // ==================== 旧格式事件处理方法 ====================

    handleStart(payload) {
        console.log('处理开始事件:', payload);
        // 可以在这里显示开始分析的状态
    }

    handleStreamStart(payload) {
        console.log('处理流开始事件:', payload);
        // 可以在这里显示流开始的状态
    }

    handleComplete(payload) {
        console.log('处理完成事件:', payload);

        // 保存最终的状态数据
        if (payload.data) {
            this.updateStateData(payload.data);
        }

        // 如果是分析阶段完成，保存分析结果
        if (payload.stage === 'analysis' && payload.analysis_result) {
            console.log('保存分析结果:', payload.analysis_result);

            // 将分析结果保存到state中
            Object.assign(this.state, payload.analysis_result);

            // 确保关键字段不为空，并从payload.analysis_result中获取
            if (payload.analysis_result.core_intent) {
                this.state.core_intent = payload.analysis_result.core_intent;
            }
            if (payload.analysis_result.user_profile) {
                this.state.user_profile = payload.analysis_result.user_profile;
            }
            if (payload.analysis_result.user_memories) {
                this.state.user_memories = payload.analysis_result.user_memories;
            }
            if (payload.analysis_result.travel_preferences) {
                this.state.travel_preferences = payload.analysis_result.travel_preferences;
            }
            if (payload.analysis_result.preference_profile) {
                this.state.preference_profile = payload.analysis_result.preference_profile;
            }
            if (payload.analysis_result.driving_context) {
                this.state.driving_context = payload.analysis_result.driving_context;
            }
            if (payload.analysis_result.vehicle_info) {
                this.state.vehicle_info = payload.analysis_result.vehicle_info;
            }
            if (payload.analysis_result.multi_city_strategy) {
                this.state.multi_city_strategy = payload.analysis_result.multi_city_strategy;
            }

            console.log('更新后的state:', this.state);
        }

        // 如果当前是分析阶段，显示立即规划按钮
        if (this.currentPhase === 'analysis') {
            console.log('分析阶段完成，显示立即规划按钮');
            this.showStartPlanningButton();
        }

        // 关闭当前的SSE连接
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
    }

    handleAnalysisStep(payload) {
        // 处理分析步骤事件
        console.log('处理分析步骤:', payload);

        const stepType = payload.step_type;
        const title = payload.title;
        const content = payload.content;
        const completed = payload.completed;

        // 保存状态数据
        if (payload.data) {
            this.updateStateData(payload.data);
        }

        if (stepType && completed) {
            // 更新对应的分析步骤
            this.completeAnalysisStep(stepType, content);

            // 检查是否所有分析步骤都完成了
            const allCompleted = Object.values(this.analysisSteps).every(step => step.completed);
            if (allCompleted) {
                console.log('所有分析步骤完成，显示立即规划按钮');
                this.showStartPlanningButton();
            }
        }
    }

    updateStateData(data) {
        // 更新状态数据
        if (data.core_intent) {
            this.state.core_intent = data.core_intent;
        }
        if (data.user_profile) {
            this.state.user_profile = data.user_profile;
        }
        if (data.user_memories) {
            this.state.user_memories = data.user_memories;
        }
        if (data.travel_preferences) {
            this.state.travel_preferences = data.travel_preferences;
        }
        if (data.preference_profile) {
            this.state.preference_profile = data.preference_profile;
        }
        if (data.driving_context) {
            this.state.driving_context = data.driving_context;
        }
        if (data.vehicle_info) {
            this.state.vehicle_info = data.vehicle_info;
        }
        if (data.multi_city_strategy) {
            this.state.multi_city_strategy = data.multi_city_strategy;
        }

        console.log('状态数据已更新:', this.state);
    }

    handleThinkingStep(payload) {
        // 根据思考步骤更新UI
        console.log('处理思考步骤:', payload);

        // 映射后端的中文分类到前端的步骤
        const categoryMapping = {
            '出行对象': 'user_intent',
            '其他': 'user_intent', // 用户画像分析归类为"其他"，映射到用户需求
            '景点推荐': 'poi_preference',
            '美食推荐': 'food_preference',
            '住宿推荐': 'accommodation_preference'
        };

        const stepKey = categoryMapping[payload.category];
        if (stepKey) {
            this.updateAnalysisStep(stepKey, payload.content);
        } else {
            console.log('未映射的分类:', payload.category, payload.content);
        }

        // 检查是否是分析完成的信号
        if (payload.content && payload.content.includes('分析阶段完成')) {
            console.log('检测到分析阶段完成，显示立即规划按钮');
            this.showStartPlanningButton();
        }
    }

    handleToolCall(payload) {
        console.log('工具调用:', payload.tool_name, payload.parameters);
    }

    handleToolResult(payload) {
        console.log('工具结果:', payload.tool_name, payload.success);
    }

    handleFinalItinerary(payload) {
        // 显示最终行程
        this.displayRealItinerary(payload);
        this.currentPhase = 'completed';
        this.updateUI();

        // 关闭SSE连接
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }

        // TTS播报
        if (window.ttsManager && payload.summary) {
            window.ttsManager.speakItineraryInfo(payload.summary.title, payload.summary.description);
        }
    }

    handleItineraryGenerated(payload) {
        console.log('行程生成中:', payload);

        // 切换到规划阶段视图
        this.currentPhase = 'planning';
        this.updateUI();

        // 更新进度
        const progress = payload.progress || 80;
        this.updatePlanningProgress(progress, '正在生成详细行程...');
    }

    handlePlanningCompleted(payload) {
        console.log('规划完成:', payload);

        // 切换到完成状态
        this.currentPhase = 'completed';
        this.updateUI();

        // 显示完成的行程
        if (payload.summary) {
            this.displayItinerarySummary(payload.summary);
        }
    }

    updatePlanningProgress(progress, message) {
        // 更新规划状态文本
        const title = document.getElementById('analysisStatusTitle');
        const desc = document.getElementById('analysisStatusDesc');

        if (title) title.textContent = message || '正在生成旅行方案...';
        if (desc) desc.textContent = `进度: ${progress}%`;
    }

    displayItinerarySummary(summary) {
        console.log('显示行程摘要:', summary);
        // 这里可以添加显示行程摘要的逻辑
        // 暂时显示一个简单的提示
        this.showAlert('行程规划完成！', 'success');
    }

    handleError(payload) {
        console.error('规划错误:', payload);
        this.showAlert('规划过程中发生错误: ' + payload.error_message, 'danger');
        this.currentPhase = 'waiting';
        this.updateUI();

        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
    }

    updateAnalysisStep(stepKey, content) {
        const stepElement = document.querySelector(`[data-step="${stepKey}"]`);
        if (stepElement) {
            // 标记为活跃状态
            stepElement.classList.add('active');

            // 更新结果内容
            const resultElement = stepElement.querySelector('.analysis-result');
            if (resultElement) {
                resultElement.innerHTML = content;
            }

            // 更新状态为完成
            const statusElement = stepElement.querySelector('.analysis-status');
            if (statusElement) {
                statusElement.innerHTML = '<i class="fas fa-check-circle text-success"></i>';
            }

            // 标记为完成
            stepElement.classList.add('completed');
            this.analysisSteps[stepKey].completed = true;

            // 检查是否所有分析步骤都完成了
            const allStepsCompleted = ['user_intent', 'poi_preference', 'food_preference', 'accommodation_preference']
                .every(key => this.analysisSteps[key].completed);

            if (allStepsCompleted) {
                // 显示用户画像
                this.showUserProfile();
            }

            // 检查是否所有步骤都完成了
            const allCompleted = Object.values(this.analysisSteps).every(step => step.completed);
            if (allCompleted) {
                this.showStartPlanningButton();

                // 所有分析步骤完成后，关闭SSE连接
                console.log('所有分析步骤完成，关闭SSE连接');
                if (this.eventSource) {
                    this.eventSource.close();
                    this.eventSource = null;
                }
            }
        }
    }

    showUserProfile() {
        console.log('showUserProfile方法被调用');
        // 用户画像数据已经通过分析步骤显示在四个分析框中
        // 不需要额外的userProfileItem元素
        console.log('用户画像数据已通过分析步骤显示');

        // 从后端获取用户画像数据（如果需要）
        this.fetchUserProfile();
    }

    async fetchUserProfile() {
        console.log('fetchUserProfile方法被调用');
        try {
            console.log('开始获取用户画像数据');
            const response = await fetch('/api/travel/user_profile', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            console.log('API响应状态:', response.status);
            if (response.ok) {
                const userProfile = await response.json();
                console.log('获取到用户画像数据:', userProfile);
                this.displayUserProfile(userProfile);
            } else {
                console.error('获取用户画像失败:', response.statusText);
                // 使用模拟数据作为后备
                this.displayUserProfile(this.getMockUserProfile());
            }
        } catch (error) {
            console.error('获取用户画像出错:', error);
            // 使用模拟数据作为后备
            this.displayUserProfile(this.getMockUserProfile());
        }
    }

    getMockUserProfile() {
        return {
            basic_info: {
                age: "25-35岁",
                gender: "女性",
                occupation: "白领",
                travel_companion: "亲子"
            },
            tags: ["慢节奏", "趣味性", "儿童友好", "舒适型"],
            budget_preference: "舒适型",
            preferences: {
                travel_style: "深度游",
                season: "春季",
                duration: "3天"
            },
            recommendation_reason: "根据您的亲子出行需求和舒适型预算偏好，我们为您推荐了适合家庭的景点和活动，确保既有趣味性又能让孩子们开心游玩。"
        };
    }

    displayUserProfile(userProfile) {
        console.log('displayUserProfile方法被调用，数据:', userProfile);

        // 更新基本信息
        const userBasicInfo = document.getElementById('userBasicInfo');
        if (userBasicInfo && userProfile.basic_info) {
            const basicInfo = userProfile.basic_info;
            const infoText = [
                basicInfo.age,
                basicInfo.gender,
                basicInfo.occupation,
                basicInfo.travel_companion
            ].filter(Boolean).join('，');
            userBasicInfo.textContent = infoText || '暂无信息';
        }

        // 更新旅行风格
        const userTravelStyle = document.getElementById('userTravelStyle');
        if (userTravelStyle) {
            userTravelStyle.textContent = userProfile.preferences?.travel_style || userProfile.travel_style || '休闲';
        }

        // 更新兴趣标签
        const userTags = document.getElementById('userTags');
        if (userTags && userProfile.tags) {
            userTags.textContent = userProfile.tags.join('，') || '暂无标签';
        }

        // 更新偏好设置
        const userPreferences = document.getElementById('userPreferences');
        if (userPreferences && userProfile.preferences) {
            const prefs = [];
            if (userProfile.preferences.accommodation_preferences) {
                prefs.push(`住宿：${userProfile.preferences.accommodation_preferences.join('，')}`);
            }
            if (userProfile.preferences.transportation_preferences) {
                prefs.push(`交通：${userProfile.preferences.transportation_preferences.join('，')}`);
            }
            userPreferences.textContent = prefs.join('；') || '暂无特殊偏好';
        }

        // 更新预算偏好
        const userBudget = document.getElementById('userBudget');
        if (userBudget) {
            userBudget.textContent = userProfile.budget_preference || '中等';
        }

        console.log('用户画像显示完成');
    }

    displayBasicInfo(basicInfo) {
        const basicInfoTags = document.getElementById('basicInfoTags');
        if (basicInfoTags && basicInfo) {
            const tags = [];
            if (basicInfo.age) tags.push(basicInfo.age);
            if (basicInfo.gender) tags.push(basicInfo.gender);
            if (basicInfo.occupation) tags.push(basicInfo.occupation);
            if (basicInfo.travel_companion) tags.push(basicInfo.travel_companion);

            basicInfoTags.innerHTML = tags.map(tag =>
                `<span class="profile-tag basic-info">${tag}</span>`
            ).join('');
        }
    }

    displayPreferenceTags(tags) {
        const preferencesTags = document.getElementById('preferencesTags');
        if (preferencesTags && tags.length > 0) {
            preferencesTags.innerHTML = tags.map(tag =>
                `<span class="profile-tag">${tag}</span>`
            ).join('');
        }
    }

    displayBudgetPreference(budgetPreference) {
        const budgetTags = document.getElementById('budgetTags');
        if (budgetTags && budgetPreference) {
            budgetTags.innerHTML = `<span class="profile-tag budget">${budgetPreference}</span>`;
        }
    }

    displayRecommendationReason(reason) {
        const recommendationText = document.getElementById('recommendationText');
        if (recommendationText && reason) {
            recommendationText.textContent = reason;
        }
    }

    displayRealItinerary(itineraryData) {
        // 从真实数据中提取信息
        const summary = itineraryData.summary || {};
        const dailyPlans = itineraryData.daily_plans || [];
        const budgetEstimation = itineraryData.budget_estimation || {};

        // 更新行程标题和描述
        document.getElementById('itineraryTitle').textContent = summary.title || '旅行行程';
        document.getElementById('itineraryDescription').textContent = summary.description || '个性化旅行方案';

        // 更新统计信息
        document.getElementById('totalDays').textContent = summary.days || dailyPlans.length;
        document.getElementById('totalPOIs').textContent = this.countTotalPOIs(dailyPlans);

        // 格式化预算信息
        const budgetText = this.formatBudget(budgetEstimation);
        document.getElementById('estimatedBudget').textContent = budgetText;

        // 更新天气信息
        const weatherInfo = this.extractWeatherInfo(itineraryData);
        document.getElementById('weatherInfo').textContent = weatherInfo;

        this.currentItinerary = itineraryData;
    }

    countTotalPOIs(dailyPlans) {
        let total = 0;
        dailyPlans.forEach(day => {
            if (day.pois) {
                total += day.pois.length;
            }
        });
        return total;
    }

    formatBudget(budgetEstimation) {
        if (budgetEstimation.total_min && budgetEstimation.total_max) {
            return `¥${budgetEstimation.total_min}-${budgetEstimation.total_max}`;
        } else if (budgetEstimation.total_min) {
            return `¥${budgetEstimation.total_min}+`;
        }
        return '待计算';
    }

    extractWeatherInfo(itineraryData) {
        // 从工具结果中提取天气信息
        if (itineraryData.weather_forecast && itineraryData.weather_forecast.length > 0) {
            const weather = itineraryData.weather_forecast[0];
            return weather.weather || weather.dayweather || '晴';
        }
        return '晴';
    }

    showAlert(message, type = 'info') {
        // 创建Bootstrap警告框
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 1050; max-width: 400px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // 插入到页面
        document.body.appendChild(alertDiv);
        
        // 3秒后自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    }
    
    async saveItinerary() {
        if (!this.currentItinerary) return;
        
        try {
            // 这里可以实现保存逻辑
            this.showAlert('行程已保存', 'success');
            
            // TTS播报
            if (window.ttsManager) {
                window.ttsManager.speakStatusUpdate('行程已保存');
            }
        } catch (error) {
            this.showAlert('保存失败: ' + error.message, 'danger');
        }
    }
    
    editItinerary() {
        if (!this.currentItinerary) return;
        
        // 这里可以实现编辑逻辑
        this.showAlert('编辑功能开发中...', 'info');
    }
    
    shareItinerary() {
        if (!this.currentItinerary) return;
        
        // 生成分享链接
        const shareUrl = `${window.location.origin}/share/${this.currentTraceId}`;
        
        if (navigator.share) {
            navigator.share({
                title: this.currentItinerary.title,
                text: this.currentItinerary.description,
                url: shareUrl
            });
        } else {
            // 复制到剪贴板
            navigator.clipboard.writeText(shareUrl).then(() => {
                this.showAlert('分享链接已复制到剪贴板', 'success');
            });
        }
    }
    
    async loadUserHistory() {
        // 这里可以实现加载用户历史行程的逻辑
    }
    
    showHistory() {
        // 显示历史行程模态框
        const modal = new bootstrap.Modal(document.getElementById('historyModal'));
        modal.show();
    }

    // 保持向后兼容的旧格式事件处理
    handleLegacyFormatEvent(eventType, payload) {
        if (!eventType || !payload) return;
        
        console.log('处理旧格式事件:', eventType, payload);

        switch (eventType) {
            case 'start':
                this.handleStart(payload);
                break;
            case 'stream_start':
                this.handleStreamStart(payload);
                break;
            case 'thinking_step':
                this.handleThinkingStep(payload);
                break;
            case 'analysis_step':
                this.handleAnalysisStep(payload);
                break;
            case 'tool_call':
                this.handleToolCall(payload);
                break;
            case 'tool_result':
                this.handleToolResult(payload);
                break;
            case 'itinerary_generated':
                this.handleItineraryGenerated(payload);
                break;
            case 'planning_completed':
                this.handlePlanningCompleted(payload);
                break;
            case 'final_itinerary':
                this.handleFinalItinerary(payload);
                break;
            case 'complete':
                this.handleComplete(payload);
                break;
            case 'error':
                this.handleError(payload);
                break;
            default:
                console.log('未知的旧格式事件类型:', eventType);
        }
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new TravelPlannerAppRefactored();
    window.travelPlannerApp = window.app; // 为PlanningPhaseManager提供引用
});
