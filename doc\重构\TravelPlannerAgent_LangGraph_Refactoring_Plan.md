# Travel Planner Agent LangGraph 重构方案

## 1. 概述

本文档旨在为 `travel_planner_agent.py` 的重构提供一份详细的技术方案和实施蓝图。本次重构的核心目标是将现有的、单体式的Agent实现，迁移到基于 **LangGraph** 的现代化、模块化、可维护的架构上。

重构将遵循以下核心原则：

*   **架构升级**：采用 LangGraph 对核心工作流进行建模，提升流程的健壮性和可观测性。
*   **能力原子化**：将意图理解、用户画像、地图服务、POI处理、智能决策等核心能力封装成独立的、可复用的原子服务。
*   **100%接口兼容**：在重构内部实现的同时，必须确保对前端的SSE流式接口（事件类型、数据结构）和最终输出的数据结构100%兼容，不引入任何破坏性变更。这是本次重构的最高优先级约束。
*   **数据流一致**：完全遵循 `doc/sql/数据流转与记忆体系.md` 中定义的数据流转和记忆沉淀规范。

### 1.1. 核心场景假设：固定汽车自驾 (Core Scenario Assumption: Fixed Vehicle Self-Driving)

**根本前提**: 本方案所有设计，特别是后台规划阶段的逻辑，均强依赖于一个核心业务场景——**用户采用固定车辆进行自驾出游**。

这一定位深刻地影响着Agent的规划逻辑，使其从一个通用的旅行规划工具，转变为一个专业的**"自驾领航员"**。Agent的关注点将从"如何组合交通工具"转变为"如何围绕用户的车辆，规划出一条安全、合理、无续航焦虑的路线"。所有关于节点、服务和状态的设计，都将服务于这一核心场景。

## 2. 核心挑战与约束

*   **前端接口兼容性**：LangGraph 的原生流式输出（State快照）与我们现有的基于 `StreamEvent` 的SSE协议不直接兼容。必须设计一个适配器层来解决此问题，确保前端无感知。
*   **高德API直连**：为了获取新UI所需的POI图片、营业时间等丰富信息，必须采用直连高德API的方式，而非旧的MCP服务。新的 `AmapService` 需要基于此进行设计。
*   **无缝替换**：新的LangGraph Agent必须能够无缝替换旧的 `TravelPlannerAgent`，而不需要对项目的其他部分（如API路由、数据库客户端等）进行大规模修改。
*   **状态管理**：需要设计一个能在整个Graph中流转的、结构化的State对象，以替代原有的 `AgentState` 类。

## 3. 交互与工作流演进

根据最新的交互设计，Agent的工作模式从"一步到位"的后台规划，演变为"交互式、分步细化"的分析引导模式。用户发起请求后，Agent将首先通过一系列链式LLM调用，对用户的偏好进行逐层分析，并将每一步的分析结果实时流式返回给前端。在用户确认分析结果后，再进入后台进行实际的工具调用和行程规划。

### 3.1. 新的LangGraph工作流：支持双模运行

为了支持交互式和全自动两种模式，工作流将演变为一个更智能的、包含条件分支的图。它能够根据初始输入决定是暂停等待用户，还是自主完成规划。

```mermaid
graph TD
    A[Start] --> B(Node 0: 宏观策略分析);
    
    subgraph "阶段A: 交互式分析 (流式返回UI)"
      B --> C{是交互模式且需要澄清?};
      C -- Yes --> D[等待用户输入];
      C -- No --> E(Node 1: 解析核心意图);
      D --> E;
      E --> F(Node 2: 分析驾驶情境);
      F --> G(Node 3: 分析偏好);
      G --> H{是交互模式?};
    end

    H -- Yes --> I[等待用户点击'立即规划'];
    H -- No --> J[开始规划 10%];
    I -- 点击 '立即规划' --> J;
    I -- 点击 '取消' --> Z[End];

    subgraph "阶段B: 后台规划 (流式构建行程并同步进度)"
        J --> K(Node 5: POI规划 35%);
        K --> L(Node 6: 住宿规划 50%);
        L --> M(Node 7: 路线优化与充电规划 75%);
        M --> N(Node 8: 整合与时间编排 90%);
        N --> O[行程规划完成 95%];
        O --> P(Node_Final: 归档与触发记忆沉淀 100%);
        P --> Z;
    end
```
*图1：支持双模运行的演进后工作流*

## 4. 提示词管理策略 (Prompt Management Strategy)

借鉴优秀项目（如`deer-flow-main`）的设计思想，我们将提示词（Prompts）作为独立的模板文件进行管理，而不是在Python代码中硬编码。这种方式将代码与内容分离，极大地提升了提示词的可维护性和可迭代性。

### 4.1. 提示词即模板文件

所有Agent的提示词将以`.md`文件的形式存储在`src/prompts/`目录下。对于旅行规划Agent，我们将创建：
*   `src/prompts/travel_planner/01_core_intent_analyzer.md`
*   `src/prompts/travel_planner/01a_multi_city_strategy_analyzer.md`
*   `src/prompts/travel_planner/01b_driving_context_analyzer.md`
*   `src/prompts/travel_planner/02_attraction_preference_analyzer.md`
*   `src/prompts/travel_planner/03_food_preference_analyzer.md`
*   `src/prompts/travel_planner/04_accommodation_preference_analyzer.md`
*   `src/prompts/travel_planner/05_poi_scorer.md`
*   `src/prompts/travel_planner/06_final_itinerary_generator.md`
*   `src/prompts/travel_planner/07_step_narrator.md`

### 4.2. 提示词加载与注入

我们将创建一个通用的、可复用的提示词加载器 `src/prompts/loader.py`，使用`Jinja2`模板引擎来动态注入变量。

```python
# src/prompts/loader.py (示例)
from pathlib import Path
import jinja2

PROMPT_ROOT = Path(__file__).parent
loader = jinja2.FileSystemLoader(PROMPT_ROOT)
env = jinja2.Environment(loader=loader)

def get_prompt(template_name: str, **kwargs) -> str:
    template = env.get_template(template_name)
    return template.render(**kwargs)
```

原子服务在执行时，会通过`get_prompt`加载模板，并将上下文变量（如用户查询、历史记录、JSON Schema）注入，生成最终的提示词。

## 5. 模块化拆分方案

为了实现原子化和高内聚，我们将创建新的目录结构，并对现有代码进行迁移和拆分。这一结构的核心思想是明确区分**内部实现**与**外部契约**。

**建议的新目录结构：**

```
src/
├── agents/
│   ├── travel_planner_lg/  # 新的LangGraph Agent目录
│   │   ├── __init__.py
│   │   ├── graph.py        # LangGraph图定义和编译
│   │   ├── nodes.py        # 图中所有节点的具体实现
│   │   ├── state.py        # 定义图的状态对象
│   │   ├── stream_adapter.py # SSE流适配器
│   │   └── schemas.py      # 新增：定义对外的SSE事件载荷(Payload)数据契约
│   └── travel_planner_agent.py # (将被废弃)
│
├── services/                 # 新增：原子化服务目录
│   ├── __init__.py
│   ├── analysis_service.py     # 交互式分析服务 (包含链式LLM调用)
│   ├── user_profile_service.py # 用户画像服务
│   ├── amap_service.py         # 高德地图服务 (直连API)
│   ├── reasoning_service.py    # 决策与编排服务
│   ├── memory_service.py       # 记忆存储服务
│   └── web_search_service.py   # 新增：Web搜索服务
│
├── prompts/                  # 新增：提示词管理目录
│   ├── __init__.py
│   ├── loader.py             # 提示词加载器
│   └── travel_planner/       # 旅行规划Agent的提示词
│       ├── 01_core_intent_analyzer.md
│       ├── 01a_multi_city_strategy_analyzer.md
│       ├── 01b_driving_context_analyzer.md
│       ├── 02_attraction_preference_analyzer.md
│       ├── 03_food_preference_analyzer.md
│       ├── 04_accommodation_preference_analyzer.md
│       ├── 05_poi_scorer.md
│       └── ...
│
└── tools/
    └── travel_planner/
        ├── __init__.py
        ├── tool_schemas.py   # 调整：定义对内的、给LLM使用的工具输入(Function Calling)规范
        ├── format_tools.py      # (将被废弃或重构)
        └── format_tools_phase3.py # (可部分复用)
```

通过这种划分，与**内部工具链**相关的Schema定义（`tool_schemas.py`）和与**Agent对外接口**相关的数据契约（`schemas.py`）被清晰地分离开，使项目结构更加合理和易于维护。

## 6. LangGraph 工作流设计

### 6.1. State 定义 (`state.py`)

我们将定义一个 `TravelPlanState` (使用 `TypedDict`) 来在整个图的节点之间传递数据。

```python
from typing import TypedDict, List, Dict, Any, Optional, Literal
from src.models.travel_planner import UserProfile, TravelItinerary

class TravelPlanState(TypedDict):
    # 输入
    trace_id: str
    user_id: str
    original_query: str
    # 新增：运行模式标志，在图的入口设定
    execution_mode: Literal["interactive", "automatic"]

    # 将单一目的地改为列表，以支持多目的地
    destinations: List[str] 

    # 新增：宏观的多城策略
    multi_city_strategy: Optional[Dict[str, Any]] # e.g., {"order": ["Quanzhou", "Xiamen"], "split": [{"city": "Quanzhou", "days": 2}, {"city": "Xiamen", "days": 3}]}

    # 阶段A (交互式分析) 输出
    core_intent: Dict[str, Any]
    attraction_preferences: Dict[str, Any]
    food_preferences: Dict[str, Any]
    accommodation_preferences: Dict[str, Any]
    user_profile: Optional[UserProfile]
    
    # 新增: 用于在节点间传递和驱动前端语音播报的对话文本
    current_narration_text: Optional[str]

    # --- 自驾场景核心状态 ---
    # 车辆信息，在分析阶段早期从用户画像服务获取
    user_vehicle_info: Optional[Dict[str, Any]] # e.g., {"model": "Tesla Model Y", "nominal_range_km": 450, "charge_type": "fast"}
    # 驾驶策略标志，由分析节点设定，指导后续所有节点行为
    driving_strategy: Literal["range_aware", "general_assistance"]
    # 规划用的实际续航里程（已乘以保守系数）
    planning_range_km: Optional[float] 
    # 续航保守系数，默认为一个标准值，未来可由用户交互调整
    range_buffer_factor: float

    # 阶段B (后台规划) 中间产物
    # 原有的扁平化POI列表将被更结构化的 `city_plan_results` 替代
    # planned_attractions: Optional[List[Dict[str, Any]]]
    # planned_restaurants: Optional[List[Dict[str, Any]]]
    # planned_accommodation: Optional[List[Dict[str, Any]]]

    # 新增: 结构化存储每个城市的POI规划结果，取代旧的扁平列表
    city_plan_results: List[CityPlanResult]
    
    # 新增：专门存储路线和充电规划的结果
    planned_charging_stops: Optional[List[Dict[str, Any]]]
    driving_routes: Optional[List[Dict[str, Any]]] # 包含各站点间驾驶路线和耗时的列表

    # 阶段B (后台规划) 最终输出
    orchestrated_itinerary: Optional[Dict[str, Any]]
    final_itinerary: Optional[TravelItinerary]
    
    # 控制与错误处理
    # user_feedback 从 'proceed'/'cancel' 扩展为可以接收更复杂的用户输入
    user_feedback: Optional[Any] 
    error: Optional[str]
    # 需要向用户澄清的信息类型, e.g., 'multi_city_strategy'
    # 如果无需澄清，则为None。这是交互模式的关键钩子。
    clarification_needed: Optional[str]
```

### 6.2. Node 定义 (`nodes.py`)

每个 `Phase` 将被实现为一个独立的`node`函数。这些函数只负责调用原子服务，并更新State。

```python
# src/agents/travel_planner_lg/nodes.py
from .state import TravelPlanState
from src.services import analysis_service, user_profile_service, amap_service, ...

def analyze_core_intent(state: TravelPlanState) -> dict:
    # 1. 调用分析服务，获得结构化数据
    core_intent = analysis_service.analyze_core_intent(state['original_query'])
    
    # [自驾场景增强]
    # 2. 调用用户画像服务获取车辆信息
    # vehicle_info = user_profile_service.get_vehicle_info(state['user_id'])
    # 3. 基于车辆信息，调用分析服务设定驾驶策略和规划续航
    # driving_context = analysis_service.analyze_driving_context(vehicle_info)

    # 4. (新增) 调用服务，将结构化数据转为有温度的自然语言旁白
    # narration_text = analysis_service.narrate_step(...)
    
    # 5. 更新State
    return {
        "destinations": ..., # 从用户查询中提取出列表
        "core_intent": core_intent,
        "current_narration_text": "...",
        # ** driving_context # Unpack driving_context dict here
    }

def analyze_multi_city_strategy(state: TravelPlanState) -> dict:
    """
    (新增策略节点)
    如果识别出多个目的地，则调用地图工具获取距离，
    然后由策略规划师LLM生成宏观策略。
    在交互模式下，设置clarification_needed标志以暂停并等待用户确认。
    在自动模式下，直接采纳建议。
    """
    if len(state.get("destinations", [])) > 1:
        # 1. 调用地图服务获取距离矩阵
        # distance_matrix = amap_service.get_distance_matrix(state["destinations"])

        # 2. 调用分析服务(策略规划师LLM)，生成策略建议
        # strategy = analysis_service.propose_multi_city_strategy(
        #     destinations=state["destinations"], 
        #     total_days=state["core_intent"]["days"],
        #     distances=distance_matrix
        # )
        # 3. 生成需要用户确认的旁白
        # narration_text = analysis_service.narrate_step(
        #     step_name="multi_city_strategy_proposal",
        #     structured_data=strategy
        # )

        # 4. 根据模式决定是否需要澄清
        needs_clarification = state.get("execution_mode") == "interactive"

        return {
            "multi_city_strategy": ..., # strategy,
            "current_narration_text": ..., # narration_text,
            "clarification_needed": "multi_city_strategy" if needs_clarification else None
        }
    # 如果是单目的地，则直接跳过
    return {}

def analyze_attraction_preferences(state: TravelPlanState) -> dict:
    # 1. 调用分析服务
    prefs = analysis_service.analyze_attraction_preferences(state['core_intent'], state.get('user_profile'))
    
    # 2. (新增) 生成旁白
    narration_text = analysis_service.narrate_step(
        step_name="attraction_analysis",
        structured_data=prefs
    )

    return {
        "attraction_preferences": prefs,
        "current_narration_text": narration_text
    }
    
def analyze_accommodation_preferences(state: TravelPlanState) -> dict:
    """
    分析住宿偏好。
    (新增逻辑) 如果在用户输入和历史画像中都找不到预算信息，
    则在State中设置一个标志，为未来的主动询问预留钩子。
    """
    # 1. 调用分析服务
    prefs, narration_text = analysis_service.analyze_accommodation_preferences(...)
    
    # 2. 检查预算信息是否缺失 (示例逻辑)
    budget_is_missing = "budget" not in prefs 
    
    # 3. 更新State，包括设置"需要澄清"的标志
    return {
        "accommodation_preferences": prefs,
        "current_narration_text": narration_text,
        "clarification_needed": "budget" if budget_is_missing and state.get("execution_mode") == "interactive" else None
    }

def route_after_analysis(state: TravelPlanState) -> str:
    """
    (路由函数)
    在分析节点后决定下一步走向。
    - 如果需要澄清(交互模式)，则等待用户输入。
    - 否则，直接进入下一个分析步骤。
    """
    if state.get("clarification_needed"):
        return "wait_for_user_input"
    else:
        return "continue_analysis"

def decide_planning_or_end(state: TravelPlanState) -> str:
    """条件边的逻辑：在分析的最后阶段决定是继续规划还是结束"""
    # 在自动模式下，总是继续
    if state.get("execution_mode") == "automatic":
        return "execute_planning_stage"
    
    # 在交互模式下，根据用户反馈决定
    if state.get("user_feedback") == "proceed":
        return "execute_planning_stage"
    else:
        return "__end__"

def execute_planning_stage(state: TravelPlanState) -> dict:
    # ... 调用 amap_service, reasoning_service 等 ...
    # [自驾场景增强]
    # 此处的服务调用将强依赖于 state['driving_strategy']
    # if state['driving_strategy'] == 'range_aware':
    #     # 执行精准续航规划
    # else:
    #     # 执行通用驾驶辅助
    # ...

    # [多目的地场景增强]
    # 这里的POI规划逻辑需要在一个循环中，按 multi_city_strategy 的城市顺序和天数逐一执行
    # for city_plan in state['multi_city_strategy']['split']:
    #    city = city_plan['city']
    #    days = city_plan['days']
    #    # ... call services for this city ...

    # 最终的 `orchestrate_itinerary` 节点需要整合所有城市的规划结果，并处理好"换乘日"
    # ...
    return {"tool_results": ..., "final_itinerary": ...}

def archive_and_finalize(state: TravelPlanState) -> dict:
    """
    (新增最终节点 - 预留钩子)
    在所有规划完成后，此处将触发异步的归档和记忆沉淀流程。
    当前版本仅作为钩子预留，打印日志信息。
    """
    # TODO: 未来实现调用 memory_service.trigger_archival(state['trace_id']) 来触发后台任务
    print(f"[{state.get('trace_id')}] 任务完成，已到达归档触发点，后续将实现异步归档。")
    
    # 此节点不应再修改核心数据，返回空字典
    return {}

# --- 拆分后的后台规划节点 ---

def plan_pois(state: TravelPlanState) -> dict:
    """
    (新增节点)
    后台规划第一步：为每个城市规划核心POI（景点、餐厅）。
    此节点会根据 state['multi_city_strategy'] 循环，为每个城市分段调用 `AmapService` 和
    `ReasoningService` (数据分析师LLM) 来搜索、筛选和评分POI。
    """
    city_plans_meta = state.get("multi_city_strategy", {}).get("split", [])
    all_city_results = []

    # for city_plan_meta in city_plans_meta:
    #     city = city_plan_meta['city']
    #     # 1. 调用 amap_service 获取候选景点和餐厅
    #     # candidate_attractions = amap_service.search_pois_with_rich_details(...)
    #     # candidate_restaurants = amap_service.search_pois_with_rich_details(...)
    #     # 2. 调用 reasoning_service (小型LLM) 进行筛选和评分
    #     # scored_attractions = reasoning_service.score_pois(...)
    #     # scored_restaurants = reasoning_service.score_pois(...)
    #     # 3. 存入结果
    #     city_result = CityPlanResult(
    #         city=city,
    #         days=city_plan_meta['days'],
    #         planned_attractions=scored_attractions,
    #         planned_restaurants=scored_restaurants,
    #         planned_accommodation=None # 住宿在下一步规划
    #     )
    #     all_city_results.append(city_result)
    
    # 返回更新后的State，为演示先返回空列表
    return {"city_plan_results": []} # all_city_results

def plan_accommodation(state: TravelPlanState) -> dict:
    """
    (新增节点)
    后台规划第二步：为每个城市分段规划住宿。
    住宿是针对整个城市停留期间的，而不是每天更换。
    """
    updated_city_plans = state.get("city_plan_results", [])
    # for i, city_plan in enumerate(updated_city_plans):
    #     city = city_plan['city']
    #     # 1. 调用 amap_service 获取候选酒店
    #     # candidate_accommodations = amap_service.search_pois_with_rich_details(...)
    #     # 2. 调用 reasoning_service 选择最合适的酒店
    #     # best_accommodation = reasoning_service.select_best_accommodation(...)
    #     # 3. 更新 city_plan_results
    #     # updated_city_plans[i]['planned_accommodation'] = best_accommodation

    return {"city_plan_results": updated_city_plans}


def optimize_routes_and_charging(state: TravelPlanState) -> dict:
    """
    (新增节点)
    后台规划第三步：路线优化与充电规划。
    这是一个计算密集型步骤，将调用策略规划师LLM或专门的路线规划算法。
    它接收所有城市的POI和住宿点，解决"TSP问题"，并根据驾驶策略智能插入充电站。
    """
    # 1. 调用 reasoning_service 或 amap_service 的高级功能进行路线规划
    # driving_routes, charging_stops = reasoning_service.optimize_tsp_with_charging(...)

    return {
        "driving_routes": [], # driving_routes,
        "planned_charging_stops": [] # charging_stops
    }

def orchestrate_final_itinerary(state: TravelPlanState) -> dict:
    """
    (新增节点)
    后台规划第四步：整合所有信息，生成最终的结构化行程单。
    此节点负责将所有零散的规划结果（POI、住宿、路线、充电站）
    按照每日的格式进行最终编排，生成符合 `src.models.travel_planner.TravelItinerary` 格式的对象。
    """
    # final_itinerary = reasoning_service.generate_final_itinerary_object(
    #     city_plans=state['city_plan_results'],
    #     routes=state['driving_routes']
    # )
    return {"final_itinerary": {}} # final_itinerary

def archive_and_finalize(state: TravelPlanState) -> dict:
    """
    (最终节点)
    在所有规划完成后，此处将触发异步的归档和记忆沉淀流程。
    当前版本仅作为钩子预留，打印日志信息。
    """
    # TODO: 未来实现调用 memory_service.trigger_archival(state['trace_id']) 来触发后台任务
    print(f"[{state.get('trace_id')}] 任务完成，已到达归档触发点，后续将实现异步归档。")
    
    # 此节点不应再修改核心数据，返回空字典
    return {}
```

### 6.3. Graph 构建 (`graph.py`)

我们将使用 `StatefulGraph` 并加入`add_conditional_edges`来组装工作流。后台规划阶段将被细化为一系列串联的节点，以支持流式进度更新。

```python
# src/agents/travel_planner_lg/graph.py
from langgraph.graph import StatefulGraph, END
from .state import TravelPlanState
from . import nodes

def create_travel_planner_graph():
    workflow = StatefulGraph(TravelPlanState)
    
    # --- 添加节点 ---
    # 阶段A: 分析节点
    workflow.add_node("analyze_core_intent", nodes.analyze_core_intent)
    workflow.add_node("analyze_multi_city_strategy", nodes.analyze_multi_city_strategy)
    workflow.add_node("analyze_driving_context", nodes.analyze_driving_context)
    workflow.add_node("analyze_preferences", nodes.analyze_preferences) # 整合所有偏好分析
    workflow.add_node("wait_for_user_input", nodes.wait_for_user_input) # 等待用户输入的节点
    
    # 阶段B: 规划节点 (已拆分为细粒度节点)
    workflow.add_node("plan_pois", nodes.plan_pois)
    workflow.add_node("plan_accommodation", nodes.plan_accommodation)
    workflow.add_node("optimize_routes_and_charging", nodes.optimize_routes_and_charging)
    workflow.add_node("orchestrate_final_itinerary", nodes.orchestrate_final_itinerary)
    workflow.add_node("archive_and_finalize", nodes.archive_and_finalize)

    # --- 定义边 ---
    workflow.set_entry_point("analyze_core_intent")
    workflow.add_edge("analyze_core_intent", "analyze_multi_city_strategy")

    # 在宏观策略分析后，进行条件路由
    workflow.add_conditional_edges(
        "analyze_multi_city_strategy",
        nodes.route_after_analysis,
        {
            "wait_for_user_input": "wait_for_user_input",
            "continue_analysis": "analyze_driving_context"
        }
    )
    # 用户输入后，继续分析
    workflow.add_edge("wait_for_user_input", "analyze_driving_context")

    workflow.add_edge("analyze_driving_context", "analyze_preferences")
    
    # 在所有分析结束后，进行最终决策
    workflow.add_conditional_edges(
        "analyze_preferences",
        nodes.decide_planning_or_end,
        {
            "execute_planning_stage": "plan_pois", # 修改: 指向规划流程的第一个节点
            "__end__": END
        }
    )

    # 串联规划阶段的节点
    workflow.add_edge("plan_pois", "plan_accommodation")
    workflow.add_edge("plan_accommodation", "optimize_routes_and_charging")
    workflow.add_edge("optimize_routes_and_charging", "orchestrate_final_itinerary")
    workflow.add_edge("orchestrate_final_itinerary", "archive_and_finalize")
    workflow.add_edge("archive_and_finalize", END)

    # 编译
    return workflow.compile(checkpointer=...) # 别忘了添加持久化
```

### 6.4. Web Search 集成
新增的 `WebSearchService` 将为Agent提供访问开放世界信息的能力，以增强其决策的实时性和准确性。
*   **服务实现**: `src/services/web_search_service.py` 将封装一个或多个第三方搜索API（如 Tavily, Bing Search等）。
*   **应用场景**:
    1.  **交互式分析阶段 (Node 2-4)**: 在分析用户对景点、美食的偏好时，可以调用Web搜索来获取最新信息。例如，搜索"上海近期热门亲子活动"、"某某餐厅最新评价"等，将结果作为上下文提供给LLM，生成更具时效性的推荐。
    2.  **后台规划阶段 (Node 6)**: 在对POI进行综合评分和编排时，可以先用Web搜索核查POI的营业状态、最新评价、官方高清图片，或寻找相关的旅游攻略，从而避免推荐已关闭的地点，并丰富行程的深度与视觉吸引力。

### 6.5. 数据模型与前端契约 (Data Models & Frontend Contracts)

为了确保前后端之间通信的健壮性与明确性，所有通过SSE事件传递的数据载荷（Payload）都应有严格的数据模型定义。我们将在`src/agents/travel_planner_lg/`目录下创建一个新的`schemas.py`文件，使用Pydantic进行定义。

```python
# src/agents/travel_planner_lg/schemas.py
from pydantic import BaseModel, Field
from typing import List, Any, Dict

class AnalysisStepPayload(BaseModel):
    """阶段A: 交互式分析步骤的载荷"""
    title: str = Field(..., description="分析步骤的标题, e.g., '核心意图分析'")
    content: Dict[str, Any] = Field(..., description="该步骤分析出的具体内容")
    narration_text: Optional[str] = Field(None, description="供前端TTS播放的、有温度的旁白文本")

class PlanningProgressPayload(BaseModel):
    """阶段B: 后台规划进度的载aho荷"""
    step_name: str = Field(..., description="当前进度的描述文本, e.g., '正在规划景点信息...'")
    progress: int = Field(..., ge=0, le=100, description="当前进度的百分比")

class ItineraryUpdatePayload(BaseModel):
    """阶段B: 增量行程更新的载荷"""
    day: int
    # 此处可以使用更具体的POI Schema
    activities: List[Dict[str, Any]] = Field(default_factory=list)
    meals: List[Dict[str, Any]] = Field(default_factory=list)
    # ...

class ErrorPayload(BaseModel):
    """错误事件的载aho荷"""
    message: str = Field(..., description="向用户显示的、友好的错误信息")
    details: str | None = Field(None, description="供调试使用的详细错误信息")
```
通过定义这些Schema，`stream_adapter`的输出将变得有据可依，前端的接收和处理逻辑也会更加清晰可靠，同时我们的代码也能受益于类型检查带来的健壮性。

#### 6.5.1. 数据契约的强制性 (Enforcement of Data Contracts)

**纪律性要求**: `schemas.py` 中定义的Pydantic模型是Agent对外的**唯一合法数据契约**。这是一条需要团队严格遵守的纪律。

*   **严禁状态泄露**: 在任何情况下，都**严禁**将后端的 `TravelPlanState` 内部状态对象直接或部分泄露给前端。`TravelPlanState` 包含大量仅供后端使用的中间变量和内部结构，将其暴露会造成前后端的高度耦合。
*   **强制校验与序列化**: 所有通过 `stream_adapter` 输出到SSE事件流的 `payload`，都**必须**是 `schemas.py` 中定义的Pydantic模型实例，并经过其校验和序列化。

遵守这一原则是确保前后端边界长期清晰、降低系统维护成本、并允许后端在未来自由重构而不影响前端的关键。

### 6.5.2. 后台规划阶段 (Phase B) 详细设计

#### LLM调用策略："多LLM"协同模式 (已通过节点拆分实现)
为了提升规划质量、降低系统复杂度和成本，后台规划阶段将严格遵循**"分而治之"**的原则，采用多个、小而专注的LLM调用或服务在独立的节点中进行协同工作，而非将所有原始数据喂给一个大型LLM进行一次性处理。

*   **执行模式**: 每个规划子节点（如 `plan_pois`, `plan_accommodation`等）都遵循 **"输入State -> 调用专注服务 -> 更新State"** 的清晰流程。
    1.  **POI规划节点 (`plan_pois`)**: 使用 `AmapService` 获取候选POI，然后调用一个中小型`数据分析师LLM`对候选集进行打分和排序，并将高质量的POI列表存入`TravelPlanState`的`city_plan_results`字段中。
    2.  **住宿规划节点 (`plan_accommodation`)**: 逻辑类似，但专注于酒店POI。
    3.  **路线优化节点 (`optimize_routes_and_charging`)**: 接收高质量的POI和住宿点，调用`策略规划师LLM`或专门的算法服务，专注于**时间和空间上的复杂编排任务**。
    4.  **最终编排节点 (`orchestrate_final_itinerary`)**: 负责将所有规划好的、结构化的数据组装成最终的`TravelItinerary`对象。

#### 数据流转：过程精细化，归档不变
*   **过程精细化**: `TravelPlanState`对象会在后台规划的每一个细粒度节点后被增量更新。这种精细化的数据流转为前端实时报告精确进度（如"已完成POI规划，正在选择酒店..."）和后端精确调试提供了坚实的基础。
*   **归档不变**: 整个流程最终的数据持久化逻辑不变。`MemoryService`依然在流程末端 (`archive_and_finalize`节点) 被调用，负责将State中的最终结果和过程数据归档至MySQL和MongoDB。

#### POI图片获取与处理
为了实现UI中图文并茂的行程展示，POI图片是关键数据。
*   **数据模型**: 所有涉及POI的数据结构（包括`TravelPlanState`中的字段和最终的`TravelItinerary`模型）必须包含一个`image_urls: List[str]`字段。
*   **获取来源**: `AmapService`在获取POI详情时，应优先通过直连API提取高质量的官方图片。`WebSearchService`可作为补充，用于抓取游记攻略中的相关图片。
*   **前端交付**: `stream_adapter`在生成最终的`FINAL_ITINERARY`和过程中的`ITINERARY_UPDATE`事件时，必须确保每个POI对象都包含了图片URL列表。

## 7. 智能自驾规划策略 (Intelligent Self-Driving Planning Strategy)

为应对固定自驾这一核心场景的复杂性，Agent必须具备一套更智能、更鲁棒的规划策略。该策略的核心是根据车辆信息的完备性，在两种模式间自动切换，并引入保守系数来应对现实世界中的续航偏差。

### 7.1. 双模驾驶策略 (Dual-Mode Driving Strategy)

Agent根据能否获取到用户车辆的明确续航里程，自动选择不同的规划模式。

#### 模式一：精准续航规划 (Range-Aware Planning)
*   **触发条件**: 从用户画像中**成功获取**了明确的车辆标称续航里程 (`nominal_range_km`)。
*   **核心行为**:
    1.  **保守续航计算**: Agent不会直接使用标称续航，而是结合一个内部的**续航保守系数 (`range_buffer_factor`)**（如默认0.8），计算出一个更安全的**规划续航里程 (`planning_range_km`)**。
    2.  **主动充电规划**: 在`orchestrate_itinerary`节点，Agent将基于`planning_range_km`，像一位经验丰富的电动车主一样，精确计算能耗，**主动地、智能地**将充电站作为必要的行程节点插入到最终路线中。
    3.  **用户告知**: 在交互分析阶段，通过旁白明确告知用户："检测到您的爱车信息，考虑到实际路况和天气，我会按照标称续航的80%为您进行规划，并智能安排充电，让您全程无忧。"

#### 模式二：通用驾驶辅助 (General Driving Assistance)
*   **触发条件**: **未能获取**车辆的具体续航信息，只知道用户是自驾。
*   **核心行为**:
    1.  **被动设施推荐**: Agent不进行主动的充电规划。其核心任务转变为**围绕用户选择的核心POI（景点、餐厅、酒店），大量搜索并提供周边的便利设施信息**（如停车场、充电站）。
    2.  **辅助决策信息**: 最终的行程将包含辅助性建议，如："[景点A]附近有3个停车场，其中[停车场P1]带充电桩，评价最好。" Agent将充电决策权完全交还给用户。
    3.  **风险提示**: 在交互分析阶段，通过旁白提醒用户："本次将为您进行自驾规划。我会在每个目的地为您圈出方便的停车场和充电站。由于不清楚您车辆的具体续航，请在旅途中留意电量哦。"

### 7.2. 续航保守系数与未来交互钩子

为解决"标称续航"与"实际续航"的偏差问题，引入保守系数是关键。

*   **默认值与动态调整**: 系统会有一个默认的保守系数（如0.8）。未来可以增强`AnalysisService`，使其能够根据季节（冬天系数更低）、海拔变化等上下文，对这个系数进行动态微调。
*   **预留交互钩子 (无需立即实现)**: 当前设计下，Agent直接使用默认系数进行规划。但我们的架构已经为未来的交互式调整预留了完整的接口。
    *   **实现路径**: 未来的版本可以在分析驾驶上下文的节点中，生成带交互需求的旁白（如："默认按80%续航规划，您需要调整吗？"），并设置`clarification_needed = 'range_buffer'`。前端可以提供选项（如"经济"、"标准"、"保守"），用户选择后通过`user_feedback`传回，Agent更新`range_buffer_factor`并重新计算，即可实现闭环。这完全利用了我们已设计的交互机制。

通过这套策略，Agent的行为将极具适应性和鲁棒性，无论用户提供的信息是否完整，都能给出当下最优的、符合自驾场景的智能规划。

## 8. Amap 直连 API 服务方案

为了支持新版UI中图文并茂的行程展示，并确保规划信息的时效性与准确性，本次重构将采用**高德地图直连API**作为唯一的数据源，替代原有的MCP服务。我们将创建一个新的 `amap_service.py` 来封装这些API调用。

```python
# src/services/amap_service.py
from src.tools.Amap.map_tool import MapTool, Location, POIResult # 假设 MapTool 已调整为异步
from typing import List
import httpx

class AmapService:
    """
    封装高德地图直连API的服务。
    
    通过直接调用高德API，我们可以获取最全面、最新的POI信息，
    包括对新UI至关重要的POI图片、营业时间、评分等丰富数据。
    这与旧的MCP服务相比，能为后台规划阶段提供更高质量的数据输入。
    """
    def __init__(self):
        # 推荐使用 httpx 实现一个异步客户端，以获得更好的性能。
        # 此处使用 MapTool 仅为示例，实际实现时应确保所有IO操作为异步。
        self._client = MapTool() 
        self._async_http_client = httpx.AsyncClient(base_url="https://restapi.amap.com/v3/")

    async def search_pois_with_rich_details(self, keywords: str, city: str, page_size: int = 10) -> List[POIResult]:
        """
        通过直连API搜索POI，并获取包括图片在内的详细信息。
        
        实际实现中，这可能是一个复合操作：
        1. 调用 'place/text' API 获取基础POI列表。
        2. 遍历列表，异步并发调用 'place/detail' API 获取每个POI的详情和图片URL。
        此函数将这些操作封装，直接返回包含所有信息的POI列表。
        """
        # ... 此处为调用 httpx 实现异步API请求的逻辑 ...
        # pois = await self._client.search_pois_async(keywords=keywords, city=city, page_size=page_size)
        # ... 在此补充获取图片、详情的逻辑 ...
        return pois_with_details
    
    # ... 其他需要的地图服务方法，如 geocode_async, get_weather_info_async 等 ...

# 创建单例
amap_service = AmapService()
```
`Phase B`后台规划阶段的节点将通过这个`amap_service`来调用地图功能，从而获得高质量的数据输入。

## 9. 前端接口兼容性方案 (`stream_adapter.py`)

这是确保对前端无感知的关键。适配器的职责变得更加重要，它必须能够**差量识别（Diff）State的变化**，并生成与UI步骤对应的、精确的`StreamEvent`。除了驱动进度条的 `PLANNING_PROGRESS` 事件外，还需要引入新的 `ITINERARY_UPDATE` 事件，用于在每个规划微步骤（如景点规划）完成后，向前端推送一块可直接渲染的行程片段数据。

### 9.1. 技术澄清：LangGraph State快照 vs. SSE事件流

为了确保架构的清晰性，有必要澄清两种在重构中涉及的数据流：

*   **LangGraph State快照 (State Snapshot):** 这是LangGraph在**后端内部**使用的数据流。每当图中的一个节点（Node）执行完毕，LangGraph会生成一份当前`TravelPlanState`对象的**完整副本**（快照）并广播出去。这种快照主要用于后端的调试、追踪和日志记录，它包含了所有内部变量和中间结果，数据是冗余且面向后端的。

*   **SSE事件流 (Server-Sent Events Stream):** 这是我们**与前端**通信所使用的**标准Web协议**。它由一系列轻量、明确的事件构成。每个事件都有一个`event_type`（事件类型/通道）和一个`payload`（数据载荷），前端可以根据不同的"通道"来更新UI的不同部分（例如，`ANALYSIS_STEP`通道更新左侧思考过程，`ITINERARY_UPDATE`通道在右侧动态追加行程卡片）。

**两者的关系是"协作"而非"等同"**。直接将原始、复杂的State快照发送给前端会造成带宽浪费、暴露后端实现细节、并极大增加前端的处理复杂度。

因此，`stream_adapter.py` 的作用就是扮演一个**"翻译官"**的角色。它订阅后端的"State快照流"，通过差量对比分析出状态变化，然后将其"翻译"成前端易于消费的、干净的"SSE事件流"。这种**适配器模式**是一种"良性复杂度"，它通过明确的层级分离，有效降低了整个系统的耦合度和总复杂度，是实现我们新版交互UI的最佳实践。

```python
# src/agents/travel_planner_lg/stream_adapter.py
import uuid
from typing import AsyncGenerator, Dict, Any
from .state import TravelPlanState
from src.models.travel_planner import StreamEvent, EventType, ItineraryUpdatePayload, PlanningProgressPayload

async def adapt_graph_stream_to_sse(...) -> AsyncGenerator[StreamEvent, None]:
    """
    将LangGraph的State流适配为SSE事件流。
    核心职责：
    1. 差量对比State，识别变化。
    2. 将一个后端State变化，转换为多个、对前端UI友好的原子化更新事件。
    3. 使用不同的 event_type 作为"通道"，指导前端更新UI的不同部分。
    """
    
    previous_state = {}
    
    async for graph_event in graph_stream:
        # Warning: This is a simplified representation.
        # The actual `graph_event` from LangGraph contains more metadata.
        # We are interested in the `values` of the state after a node has run.
        node_name = list(graph_event.keys())[0]
        current_state_update = graph_event[node_name]
        
        # 差量对比逻辑，检测到哪个新字段被添加或更新
        new_keys = current_state_update.keys() - previous_state.keys()
        
        for key in new_keys:
            # 根据新增的key，生成对应的UI更新事件
            # ----------------------------------------------------
            # 通道1: ANALYSIS_STEP (驱动UI左侧的思考过程)
            # ----------------------------------------------------
            if key in ["core_intent", "attraction_preferences", "food_preferences", "accommodation_preferences"]:
                 # 从State中提取旁白文本
                 narration = current_state_update.get("current_narration_text")
                 payload = AnalysisStepPayload(
                     title=..., # 根据key生成标题
                     content=current_state_update[key],
                     narration_text=narration
                 )
                 yield StreamEvent(event_type=EventType.ANALYSIS_STEP, payload=payload)

            # ----------------------------------------------------
            # 通道2 & 3: PLANNING_PROGRESS & ITINERARY_UPDATE (协同驱动UI右侧的行程构建)
            # 一个后台节点的完成，会触发两个事件，分别更新进度和内容
            # ----------------------------------------------------
            elif key == "planned_attractions":
                 # 1. (进度通道) 推送进度更新
                 yield StreamEvent(
                    event_type=EventType.PLANNING_PROGRESS, 
                    payload=PlanningProgressPayload(step_name="已完成景点规划...", progress=35)
                 )
                 # 2. (行程通道) 推送可渲染的行程卡片数据
                 yield StreamEvent(
                    event_type=EventType.ITINERARY_UPDATE,
                    payload=ItineraryUpdatePayload(day=1, activities=current_state_update[key]) # 载荷为该片段的数据
                 )
            elif key == "planned_restaurants":
                 # 类似地，推送进度和餐厅相关的行程卡片
                 yield StreamEvent(
                    event_type=EventType.PLANNING_PROGRESS, 
                    payload=PlanningProgressPayload(step_name="已完成美食规划...", progress=50)
                 )
                 yield StreamEvent(
                    event_type=EventType.ITINERARY_UPDATE,
                    payload=ItineraryUpdatePayload(day=1, meals=current_state_update[key])
                 )

        # 更新状态，为下一次差量对比做准备
        previous_state.update(current_state_update)

    # ... 在流程末尾，根据 final_itinerary 生成最终的 FINAL_ITINERARY 事件 ...
```
在API路由层，我们将调用这个适配器，从而在重构后端的同时，完美兼容现有前端应用的交互逻辑。

## 10. 数据流转与记忆体系 (异步解耦架构)

为了确保主流程的性能和可靠性，记忆沉淀将采用异步解耦架构，严格遵循PRD中的最终定义。这取代了原有的同步处理模式。

#### 10.1. 架构设计

图中的最后一个业务节点 (例如 `orchestrate_itinerary` 或一个新增的 `finalize_task` 节点) 的职责将被严格限定在两个快速、非阻塞的操作内：

1.  **快速归档 (Fast Archiving)**: 调用 `memory_service.archive_log_to_mongodb` 方法。该方法负责将Redis中的完整任务上下文（对话、State变化、工具调用日志等）作为一个完整的JSON文档，快速写入**MongoDB**的`task_execution_logs`集合。这应是一个毫秒级的IO操作。

2.  **触发评估 (Trigger Evaluation)**: 在归档成功后，调用 `memory_service.trigger_memory_evaluation` 方法。该方法仅负责向一个任务队列（推荐使用Redis Pub/Sub）发布一条包含 `task_id` 的轻量级消息。

所有耗时的、基于LLM的记忆提取、评估以及向**MySQL**写入结构化记忆的操作，全部由一个或多个独立的**后台工作进程 (Background Worker)** 处理，该进程订阅前述消息队列。

这种设计将核心的、面向用户的请求响应流程与耗时的、内部的智能沉淀流程完全解耦，确保了用户体验的流畅性和系统的健壮性。

#### 10.2. 异步处理流程图

```mermaid
sequenceDiagram
    participant Main as LangGraph主流程
    participant MemoryService as MemoryService
    participant MongoDB
    participant RedisMQ as Redis消息队列
    participant Worker as 后台记忆评估Worker

    Main->>MemoryService: 调用 archive_and_trigger_evaluation(task_id)
    activate MemoryService
    MemoryService->>MongoDB: archive_log_to_mongodb(task_id, log)
    MemoryService->>RedisMQ: trigger_memory_evaluation(task_id)
    deactivate MemoryService

    Note right of Worker: Worker独立部署，长期监听
    Worker->>RedisMQ: 订阅任务
    RedisMQ-->>Worker: 接收到task_id
    activate Worker
    Worker->>MongoDB: 读取任务日志(task_id)
    MongoDB-->>Worker: 返回完整日志
    Worker->>Worker: 调用LLM评估价值
    Worker->>MySQL: 写入高价值记忆
    deactivate Worker
```
*图2：异步记忆沉淀流程图*

#### 10.3. 分阶段实施策略 (预留接口)

正如您所提议的，该体系可以分阶段实现，为记忆体的后续实现预留接口：

*   **第一阶段 (仅记录)**: 在项目初期，`memory_service` 中可以只实现 `archive_log_to_mongodb` 方法。图的最后一个节点也只调用此方法进行日志归档，**暂不发布触发评估的消息**。
    *   **收益**: 以最低的开发成本，实现了对所有交互过程数据的完整捕获和记录。这为后续的分析、调试和模型迭代积累了宝贵的数据资产。

*   **第二阶段 (完整实现)**: 在后续迭代中，当团队资源允许时，再开发后台的`记忆处理服务`和`记忆评估Agent`。完成后，只需在图的最后一个节点中**启用对 `trigger_memory_evaluation` 方法的调用**，即可无缝激活完整的异步记忆沉淀链路，而无需改动现有主流程。

这种分阶段实施的策略，将核心业务交付与高级智能功能的研发解耦，提供了极大的开发灵活性和系统稳定性。

## 11. 配置管理 (Configuration Management)

本次重构引入的新服务和外部API调用，其配置将严格遵循项目现有的管理规范。

*   **统一管理**：所有新增的配置项，如API密钥、模型名称等，都必须在 `config/default.yaml` 文件中进行定义。严禁在代码中硬编码任何配置值。
*   **API密钥**：新增的 `WebSearchService` 和 `AmapService` 所需的API Key，将在`default.yaml`中添加相应条目，例如 `amap_direct_api_key: "..."`。
*   **LLM角色矩阵配置**：为了灵活性和成本控制，我们将根据定义的LLM角色矩阵在配置中明确区分模型。服务和节点在初始化时，将从配置中读取对应的模型名称来实例化LLM客户端。
    ```yaml
    # config/default.yaml

    llm:
      # 策略规划师: 用于最高阶的推理任务
      planner_model:
        provider: openai
        name: "glm-4"
        api_key: "..."
        base_url: "..."

      # 数据分析师: 用于结构化分析
      analyst_model:
        provider: openai
        name: "glm-3-turbo"
        api_key: "..."
        base_url: "..."
      
      # 对话交互师: 用于生成旁白，要求低延迟
      narrator_model:
        provider: openai
        name: "glm-3-turbo"
        api_key: "..."
        base_url: "..."
    ```
*   **加载方式**：所有配置将通过 `src/core/config.py` 中定义的Pydantic `Settings` 类进行加载和校验，确保服务的配置注入是类型安全的。

## 12. 错误处理与鲁棒性设计 (Error Handling & Resilience)

一个健壮的Agent必须能够优雅地处理执行过程中的各种异常。我们将基于`TravelPlanState`中的`error`字段，建立一套完整的错误处理机制。

*   **节点级错误捕获**：在`nodes.py`中，每个执行实际操作的节点（如API调用、数据处理）都必须用`try...except`块包裹核心逻辑。当捕获到异常时（如API超时、返回数据格式错误等），节点不应直接抛出异常导致图中断，而应将一个对用户友好的错误信息字符串赋值给`state['error']`字段，然后正常返回更新后的`state`。

*   **图级错误流转**：在`graph.py`中，我们将利用`add_conditional_edges`实现一个全局的错误处理分支。在每个可能出错的关键节点之后，都会检查`state['error']`字段。如果该字段有值，则流程被导向一个专门的`handle_error`节点；否则，流程正常继续。`handle_error`节点负责记录详细日志并最终结束流程。

*   **前端用户通知**：`stream_adapter`在将State快照转换为SSE事件时，会优先检查`state['error']`字段。一旦发现该字段被填充，它将：
    1.  立即生成并`yield`一个类型为 `ERROR` 的`StreamEvent`，其载荷为一个包含友好错误信息的`ErrorPayload`对象。
    2.  立即停止后续所有事件的生成，结束事件流。

### 12.1. 性能与用户感知延迟管理 (新增章节)

为实现"陪伴式"语音对话，我们为每个分析节点增加了一次轻量的LLM调用用于生成旁白文本。虽然这会引入微小的物理延迟（预计在几百毫秒级别），但通过我们的流式架构，可以实现更优的用户感知性能。

*   **延迟对冲（Latency Hedging）**: 我们的 `stream_adapter` 将在每个分析节点完成后立即推送 `ANALYSIS_STEP` 事件（包含UI更新和旁白文本）。用户接收事件并等待前端播放语音所需的时间（通常为2-4秒），将有效地"隐藏"后端执行下一个分析节点的计算时间。
*   **化整为零**: 该设计将一次漫长的、完整的等待（例如10秒），分解为多次"短等待+即时反馈"的循环。这种交互模式将"无效等待时间"转化为"有效信息接收时间"，极大地降低了用户对延迟的感知，提升了系统的实时感和互动性。
*   **技术要求**: 为保证此策略有效，旁白生成LLM必须选用响应速度快、性能稳定的轻量级模型，并在配置中与核心分析LLM明确区分（见`10. 配置管理`）。

### 12.2. 状态持久化与崩溃恢复 (State Persistence & Crash Recovery)

为了实现PRD中提到的"断点续传"和系统鲁棒性，必须将每个节点的执行结果持久化。这对于长时间运行的、可能因网络波动或服务重启而中断的复杂规划任务至关重要。

**实现策略：**

1.  **Redis Checkpointing**: 我们将利用LangGraph内置的`Checkpointer`机制。一个配置好的`RedisSaver`实例将被用于图的编译过程。
2.  **自动持久化**: 在图执行的每个步骤（即每个节点成功运行）之后，LangGraph会自动将当前的`TravelPlanState`完整地序列化并保存到指定的Redis实例中。每个任务的会话将以其唯一的`trace_id`（对应LangGraph中的`thread_id`）作为键进行存储。
3.  **无缝恢复**: 当一个规划请求进入系统时，我们会使用相同的`trace_id`来调用图。如果Redis中存在该`thread_id`的检查点，LangGraph将自动加载状态并从上次中断的地方继续执行，而不是从头开始。

这将无缝地实现崩溃恢复，是保障长时间运行任务可靠性的核心。

**配置示例 (`graph.py`)**:
```python
# graph.py 中的 Checkpointer 配置示例
from langgraph.checkpoint.redis import RedisSaver

# 在应用启动时，根据配置初始化Redis连接
# memory = RedisSaver.from_conn_string("redis://user:password@host:port")
memory = RedisSaver.from_env() # 更推荐的方式，从环境变量加载

# 在编译图时传入checkpointer
app = workflow.compile(checkpointer=memory)

# 在调用图时，必须在config中传入可配置的thread_id，以启用持久化
# config = {"configurable": {"thread_id": "some_unique_trace_id"}}
# for event in app.stream(input_data, config=config):
#     ...
```

### 12.3. 服务管理与依赖注入 (Service Management & Dependency Injection)

为了避免在`nodes.py`中硬编码服务实例化（例如 `amap_service = AmapService()`），并从根本上提高代码的可测试性和可维护性，我们将采用更清晰的服务管理与依赖注入模式。

**核心原则：**

1.  **统一创建**: 所有的原子化服务（`UserProfileService`, `AmapService`等）在应用启动时（例如，在FastAPI的startup事件中或主程序入口）被统一初始化一次，并作为生命周期与应用相同的单例存在。
2.  **依赖注入**: 服务的依赖（如数据库客户端、配置对象、其他服务）应在此时通过构造函数注入。
3.  **显式传递**: 编译好的LangGraph `app`对象本身不直接依赖这些服务。相反，服务实例应该在调用图时，通过`RunnableConfig`或部分函数绑定（`functools.partial`）的方式传递给需要它们的节点。

**实现示例**:

```python
# main.py (FastAPI应用入口)
from functools import partial
from fastapi import FastAPI, Request
from src.core.config import load_config
from src.services import AmapService, AnalysisService
from src.agents.travel_planner_lg.graph import app # 编译好的LangGraph App

# 1. 启动时创建单例服务
app_state = {}
@app.on_event("startup")
def startup_event():
    config = load_config()
    app_state["amap_service"] = AmapService(api_key=config.amap_key)
    # app_state["analysis_service"] = AnalysisService(llm_manager=...)

# 2. 在API路由中，将服务注入到节点调用中
@app.post("/plan")
async def plan_travel(request: Request):
    # ...
    # LangGraph推荐使用 .with_config({"recursion_limit": N})
    # 并通过 state['agent_scratchpad'] 或上下文来传递服务实例
    # 另一种方式是使用 partial 来绑定服务
    # 以下为概念示例
    # app_with_services = app.with_config(
    #     {"run_name": "TravelPlan"},
    #     # 将service实例传入，供图中节点使用
    # )
    # ...
```
这种模式完全解耦了业务逻辑（Nodes）和外部依赖（Services），使得对每个节点进行单元测试（通过传入Mock服务）变得极其简单，是构建可维护系统的最佳实践。


## 13. 测试策略 (Testing Strategy)

为确保重构后代码的质量和稳定性，我们将实施分层测试策略。

*   **单元测试 (Unit Tests)**
    *   **测试对象**：`src/services/`下的每个原子服务、`src/prompts/loader.py`提示词加载器、`nodes.py`中不涉及IO的纯逻辑函数。
    *   **方法**：使用 `pytest` 框架。对于依赖外部IO（如API、数据库）的服务，必须使用 `pytest-mock` 或 `unittest.mock` 对外部依赖进行模拟（Mock），确保测试的独立性、幂等性和执行速度。

*   **集成测试 (Integration Tests)**
    *   **测试对象**：编译后的LangGraph `app`对象。
    *   **方法**：编写测试用例，通过调用`app.stream()`或`app.invoke()`方法，传入一个预设的初始State，然后断言（Assert）最终输出的State或State流中的关键字段是否符合预期。此测试的重点是验证**节点之间的协同工作**和**条件分支的逻辑**是否正确。

*   **端到端测试 (End-to-End Tests)**
    *   **测试对象**：从FastAPI的HTTP端点到完整SSE事件流的整个链路。
    *   **方法**：在测试环境中启动应用，使用`httpx`等HTTP客户端模拟前端请求，调用`/api/v1/travel_planner`接口。然后，在客户端接收完整的SSE事件流，并验证**事件的顺序、类型（event_type）和数据载荷（payload）**是否与预期完全一致。此测试是保障**前端接口100%兼容**的最终防线。

## 14. 可观测性与调试 (Observability & Debugging)

### 14.1. LangSmith 可视化追踪

为了极大地提升开发和调试效率，我们将引入LangChain生态的官方工具 **LangSmith** 作为首选的可观测性平台。

*   **核心价值**: LangSmith能够自动捕获并以可视化的方式展示LangGraph的每一次运行轨迹。开发者可以清晰地看到每个节点的输入、输出、耗时，以及LLM调用的完整Prompt和响应，是诊断问题、优化性能和理解复杂Agent行为的利器。
*   **实施定位**: 我们将LangSmith定位为**开发和测试阶段的必备工具**，而不是一个需要排期交付的生产环境功能。开发者从第一天起就应在本地开发环境中配置并启用LangSmith，以辅助开发和调试工作。
*   **配置**: 启用LangSmith只需在环境变量中设置相关的API密钥和项目名称即可，对现有代码无侵入性。

## 15. 实施步骤

1.  **环境与结构准备**：
    *   安装 `langgraph`, `langchain`, `jinja2`。
    *   根据新的模块化方案创建 `src/agents/travel_planner_lg/`, `src/services/`, `src/prompts/travel_planner/` 等目录。
2.  **提示词与Schema重构**：
    *   在 `src/prompts/travel_planner/` 中创建所有`.md`提示词模板。
    *   实现 `src/prompts/loader.py`。
    *   重构 `src/tools/travel_planner/`，将大的Schema拆分为 `analysis_schemas.py`