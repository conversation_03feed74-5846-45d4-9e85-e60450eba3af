"""
高德地图服务 (Amap Service)

提供高德地图API的原子化服务，包括地理编码、路径规划、POI搜索等功能。
"""

import json
import logging
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

import aiohttp
from src.core.config import get_settings

logger = logging.getLogger(__name__)


class AmapService:
    """高德地图服务类"""
    
    def __init__(self):
        """初始化高德地图服务"""
        self.config = get_settings()
        self.api_key = self.config.amap_api_key
        self.base_url = "https://restapi.amap.com/v3"
        self.session = None
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession()
        return self.session
    
    async def close(self):
        """关闭HTTP会话"""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def geocode(self, address: str, city: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        地理编码 - 将地址转换为坐标
        
        Args:
            address: 地址
            city: 城市（可选）
            
        Returns:
            地理编码结果
        """
        try:
            logger.info(f"地理编码 - 地址: {address}")
            
            session = await self._get_session()
            
            params = {
                "key": self.api_key,
                "address": address,
                "output": "json"
            }
            
            if city:
                params["city"] = city
            
            async with session.get(f"{self.base_url}/geocode/geo", params=params) as response:
                data = await response.json()
                
                if data.get("status") == "1" and data.get("geocodes"):
                    geocode = data["geocodes"][0]
                    result = {
                        "address": geocode.get("formatted_address"),
                        "location": geocode.get("location"),
                        "level": geocode.get("level"),
                        "province": geocode.get("province"),
                        "city": geocode.get("city"),
                        "district": geocode.get("district"),
                        "adcode": geocode.get("adcode")
                    }
                    
                    logger.info(f"地理编码成功 - 坐标: {result['location']}")
                    return result
                else:
                    logger.warning(f"地理编码失败 - 地址: {address}, 响应: {data}")
                    return None
                    
        except Exception as e:
            logger.error(f"地理编码异常 - 地址: {address}, 错误: {str(e)}")
            return None
    
    async def reverse_geocode(self, longitude: float, latitude: float) -> Optional[Dict[str, Any]]:
        """
        逆地理编码 - 将坐标转换为地址
        
        Args:
            longitude: 经度
            latitude: 纬度
            
        Returns:
            逆地理编码结果
        """
        try:
            logger.info(f"逆地理编码 - 坐标: {longitude},{latitude}")
            
            session = await self._get_session()
            
            params = {
                "key": self.api_key,
                "location": f"{longitude},{latitude}",
                "output": "json",
                "extensions": "all"
            }
            
            async with session.get(f"{self.base_url}/geocode/regeo", params=params) as response:
                data = await response.json()
                
                if data.get("status") == "1" and data.get("regeocode"):
                    regeocode = data["regeocode"]
                    result = {
                        "formatted_address": regeocode.get("formatted_address"),
                        "addressComponent": regeocode.get("addressComponent", {}),
                        "pois": regeocode.get("pois", []),
                        "roads": regeocode.get("roads", []),
                        "roadinters": regeocode.get("roadinters", []),
                        "aois": regeocode.get("aois", [])
                    }
                    
                    logger.info(f"逆地理编码成功 - 地址: {result['formatted_address']}")
                    return result
                else:
                    logger.warning(f"逆地理编码失败 - 坐标: {longitude},{latitude}, 响应: {data}")
                    return None
                    
        except Exception as e:
            logger.error(f"逆地理编码异常 - 坐标: {longitude},{latitude}, 错误: {str(e)}")
            return None
    
    async def get_distance_matrix(
        self,
        origins: List[str],
        destinations: Optional[List[str]] = None,
        type_: str = "1"  # 1: 直线距离, 3: 驾车路径距离
    ) -> Dict[str, Any]:
        """
        获取距离矩阵
        
        Args:
            origins: 起点列表
            destinations: 终点列表（如果为None，则使用origins）
            type_: 距离类型（1: 直线距离, 3: 驾车路径距离）
            
        Returns:
            距离矩阵
        """
        try:
            logger.info(f"获取距离矩阵 - 起点数: {len(origins)}")
            
            if destinations is None:
                destinations = origins
            
            # 先对所有地点进行地理编码
            origin_coords = []
            for origin in origins:
                geocode_result = await self.geocode(origin)
                if geocode_result and geocode_result.get("location"):
                    origin_coords.append(geocode_result["location"])
                else:
                    logger.warning(f"无法获取地点坐标: {origin}")
                    origin_coords.append(None)
            
            destination_coords = []
            for destination in destinations:
                if destination in origins:
                    # 如果目的地在起点列表中，直接使用已有坐标
                    idx = origins.index(destination)
                    destination_coords.append(origin_coords[idx])
                else:
                    geocode_result = await self.geocode(destination)
                    if geocode_result and geocode_result.get("location"):
                        destination_coords.append(geocode_result["location"])
                    else:
                        logger.warning(f"无法获取地点坐标: {destination}")
                        destination_coords.append(None)
            
            # 构建距离矩阵
            matrix = {
                "origins": origins,
                "destinations": destinations,
                "distances": [],
                "durations": [],
                "type": type_
            }
            
            session = await self._get_session()
            
            # 批量查询距离
            for i, origin_coord in enumerate(origin_coords):
                row_distances = []
                row_durations = []
                
                if origin_coord is None:
                    # 如果起点坐标无效，填充空值
                    row_distances = [None] * len(destination_coords)
                    row_durations = [None] * len(destination_coords)
                else:
                    for j, dest_coord in enumerate(destination_coords):
                        if dest_coord is None:
                            row_distances.append(None)
                            row_durations.append(None)
                            continue
                        
                        if i == j and origins == destinations:
                            # 同一地点距离为0
                            row_distances.append(0)
                            row_durations.append(0)
                            continue
                        
                        # 查询距离和时间
                        params = {
                            "key": self.api_key,
                            "origins": origin_coord,
                            "destination": dest_coord,
                            "type": type_,
                            "output": "json"
                        }
                        
                        try:
                            async with session.get(f"{self.base_url}/distance", params=params) as response:
                                data = await response.json()
                                
                                if data.get("status") == "1" and data.get("results"):
                                    result = data["results"][0]
                                    distance = int(result.get("distance", 0))  # 米
                                    duration = int(result.get("duration", 0))  # 秒
                                    
                                    row_distances.append(distance)
                                    row_durations.append(duration)
                                else:
                                    row_distances.append(None)
                                    row_durations.append(None)
                                    
                        except Exception as e:
                            logger.warning(f"查询距离失败: {origins[i]} -> {destinations[j]}, 错误: {str(e)}")
                            row_distances.append(None)
                            row_durations.append(None)
                        
                        # 添加延迟避免API限制
                        await asyncio.sleep(0.1)
                
                matrix["distances"].append(row_distances)
                matrix["durations"].append(row_durations)
            
            logger.info("距离矩阵获取完成")
            return matrix
            
        except Exception as e:
            logger.error(f"获取距离矩阵失败: {str(e)}")
            raise
    
    async def search_poi(
        self,
        keywords: str,
        city: Optional[str] = None,
        types: Optional[str] = None,
        location: Optional[str] = None,
        radius: int = 3000,
        page: int = 1,
        offset: int = 20
    ) -> List[Dict[str, Any]]:
        """
        POI搜索
        
        Args:
            keywords: 搜索关键词
            city: 城市
            types: POI类型
            location: 中心点坐标
            radius: 搜索半径（米）
            page: 页码
            offset: 每页数量
            
        Returns:
            POI搜索结果列表
        """
        try:
            logger.info(f"POI搜索 - 关键词: {keywords}, 城市: {city}")
            
            session = await self._get_session()
            
            params = {
                "key": self.api_key,
                "keywords": keywords,
                "output": "json",
                "page": page,
                "offset": offset
            }
            
            if city:
                params["city"] = city
            if types:
                params["types"] = types
            if location:
                params["location"] = location
                params["radius"] = radius
            
            async with session.get(f"{self.base_url}/place/text", params=params) as response:
                data = await response.json()
                
                if data.get("status") == "1" and data.get("pois"):
                    pois = []
                    for poi in data["pois"]:
                        poi_info = {
                            "id": poi.get("id"),
                            "name": poi.get("name"),
                            "type": poi.get("type"),
                            "typecode": poi.get("typecode"),
                            "address": poi.get("address"),
                            "location": poi.get("location"),
                            "tel": poi.get("tel"),
                            "distance": poi.get("distance"),
                            "business_area": poi.get("business_area"),
                            "citycode": poi.get("citycode"),
                            "adcode": poi.get("adcode"),
                            "photos": poi.get("photos", []),
                            "rating": poi.get("rating"),
                            "cost": poi.get("cost")
                        }
                        pois.append(poi_info)
                    
                    logger.info(f"POI搜索成功 - 找到 {len(pois)} 个结果")
                    return pois
                else:
                    logger.warning(f"POI搜索失败 - 关键词: {keywords}, 响应: {data}")
                    return []
                    
        except Exception as e:
            logger.error(f"POI搜索异常 - 关键词: {keywords}, 错误: {str(e)}")
            return []
    
    async def get_driving_route(
        self,
        origin: str,
        destination: str,
        waypoints: Optional[List[str]] = None,
        strategy: int = 0  # 0: 速度优先, 1: 费用优先, 2: 距离优先, 3: 不走高速
    ) -> Optional[Dict[str, Any]]:
        """
        获取驾车路线规划
        
        Args:
            origin: 起点
            destination: 终点
            waypoints: 途经点列表
            strategy: 路径策略
            
        Returns:
            路线规划结果
        """
        try:
            logger.info(f"驾车路线规划 - 起点: {origin}, 终点: {destination}")
            
            # 地理编码
            origin_geo = await self.geocode(origin)
            dest_geo = await self.geocode(destination)
            
            if not origin_geo or not dest_geo:
                logger.error("起点或终点地理编码失败")
                return None
            
            session = await self._get_session()
            
            params = {
                "key": self.api_key,
                "origin": origin_geo["location"],
                "destination": dest_geo["location"],
                "strategy": strategy,
                "extensions": "all",
                "output": "json"
            }
            
            if waypoints:
                waypoint_coords = []
                for waypoint in waypoints:
                    wp_geo = await self.geocode(waypoint)
                    if wp_geo:
                        waypoint_coords.append(wp_geo["location"])
                
                if waypoint_coords:
                    params["waypoints"] = ";".join(waypoint_coords)
            
            async with session.get(f"{self.base_url}/direction/driving", params=params) as response:
                data = await response.json()
                
                if data.get("status") == "1" and data.get("route"):
                    route = data["route"]
                    result = {
                        "origin": origin,
                        "destination": destination,
                        "distance": int(route.get("distance", 0)),  # 米
                        "duration": int(route.get("duration", 0)),  # 秒
                        "strategy": strategy,
                        "tolls": int(route.get("tolls", 0)),  # 过路费（元）
                        "toll_distance": int(route.get("toll_distance", 0)),  # 收费路段距离
                        "paths": route.get("paths", [])
                    }
                    
                    logger.info(f"驾车路线规划成功 - 距离: {result['distance']}米, 时间: {result['duration']}秒")
                    return result
                else:
                    logger.warning(f"驾车路线规划失败 - 响应: {data}")
                    return None
                    
        except Exception as e:
            logger.error(f"驾车路线规划异常 - 错误: {str(e)}")
            return None
