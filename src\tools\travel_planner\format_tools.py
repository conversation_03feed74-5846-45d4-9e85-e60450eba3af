"""
旅行规划Agent专用格式化工具集

严格遵循四阶段工作流的原子化Function Call工具实现
"""

from typing import Dict, Any, List, Optional
from src.tools import register_tool
import json


# === Phase 1 工具：意图理解与个性化融合 ===

@register_tool
def extract_travel_intent(user_query: str, current_datetime: str) -> str:
    """
    Phase 1: 提取旅行规划意图和关键信息的标准格式定义
    
    Args:
        user_query: 用户原始查询
        current_datetime: 当前系统时间
        
    Returns:
        JSON Schema字符串，定义了LLM应该返回的结构化数据格式
    """
    schema = {
        "type": "object",
        "description": "旅行意图提取的标准格式",
        "properties": {
            "destination": {
                "type": ["string", "null"], 
                "description": "目的地城市，如果用户没有明确提及则为null"
            },
            "origin": {
                "type": ["string", "null"],
                "description": "出发点，如果用户没有明确提及则为null"
            },
            "days": {
                "type": ["integer", "null"],
                "description": "出行天数，必须是整数类型，如果用户没有明确提及则为null",
                "minimum": 1,
                "maximum": 30
            },
            "travel_time": {
                "type": ["string", "null"],
                "description": "出行时间，可以是具体日期或相对时间"
            },
            "travelers": {
                "type": ["string", "null"],
                "description": "出行人员类型，如'情侣'、'家庭'、'朋友'、'单独'"
            },
            "budget": {
                "type": ["string", "null"],
                "description": "预算范围，如'经济型'、'中等'、'豪华'"
            },
            "preferences": {
                "type": ["array", "null"],
                "items": {"type": "string"},
                "description": "兴趣偏好列表，如['文化古迹', '美食', '自然风光']"
            },
            "special_needs": {
                "type": ["array", "null"],
                "items": {"type": "string"},
                "description": "特殊需求列表，如['亲子友好', '无障碍', '停车方便']"
            },
            "transport_mode": {
                "type": "string",
                "enum": ["driving", "walking", "transit"],
                "default": "driving",
                "description": "出行方式，默认为开车"
            },
            "accommodation_needed": {
                "type": ["boolean", "null"],
                "description": "是否需要住宿安排，基于距离和天数智能判断，如果用户未明确提及则为null"
            },
            "accommodation_preference": {
                "type": ["string", "null"],
                "description": "住宿偏好，如'经济型'、'中档'、'豪华'、'民宿'、'连锁酒店'"
            }
        },
        "required": [],
        "additionalProperties": False
    }
    return json.dumps(schema, ensure_ascii=False, indent=2)


@register_tool
def build_personalized_query(extracted_entities: Dict[str, Any], user_profile: Dict[str, Any]) -> str:
    """
    Phase 1: 构建个性化查询指令的格式定义
    
    Args:
        extracted_entities: 提取的实体信息
        user_profile: 用户画像信息
        
    Returns:
        个性化查询指令的JSON Schema
    """
    schema = {
        "type": "object",
        "description": "个性化查询指令格式",
        "properties": {
            "destination_info": {
                "type": "object",
                "properties": {
                    "city": {"type": "string"},
                    "coordinates": {
                        "type": "object",
                        "properties": {
                            "longitude": {"type": "number"},
                            "latitude": {"type": "number"}
                        }
                    }
                }
            },
            "user_preferences": {
                "type": "object",
                "properties": {
                    "has_children": {"type": "boolean"},
                    "budget_level": {"type": "string", "enum": ["low", "medium", "high"]},
                    "travel_style": {"type": "string"},
                    "interest_tags": {"type": "array", "items": {"type": "string"}}
                }
            },
            "search_priorities": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "category": {"type": "string", "enum": ["parking", "food", "attraction", "hotel", "charging"]},
                        "keywords": {"type": "array", "items": {"type": "string"}},
                        "priority": {"type": "integer", "minimum": 1, "maximum": 5}
                    }
                }
            }
        },
        "required": ["destination_info", "search_priorities"]
    }
    return json.dumps(schema, ensure_ascii=False, indent=2)


# === Phase 2 工具：四层并行调用格式定义 ===

@register_tool
def define_layer1_parallel_calls() -> str:
    """Phase 2: 第一层并行调用（基础信息层）的格式定义"""
    schema = {
        "layer": 1,
        "name": "基础信息层",
        "description": "获取规划所必需的起点、终点坐标和天气",
        "parallel_calls": [
            {
                "tool": "maps_geo",
                "purpose": "获取起点坐标",
                "params_schema": {
                    "address": {"type": "string", "description": "起点地址"},
                    "city": {"type": ["string", "null"], "description": "所在城市，可选"}
                },
                "required": True
            },
            {
                "tool": "maps_geo", 
                "purpose": "获取目的地坐标",
                "params_schema": {
                    "address": {"type": "string", "description": "目的地地址"},
                    "city": {"type": ["string", "null"], "description": "所在城市，可选"}
                },
                "required": True
            },
            {
                "tool": "maps_weather",
                "purpose": "获取目的地天气",
                "params_schema": {
                    "city": {"type": "string", "description": "目的地城市"}
                },
                "required": True
            }
        ]
    }
    return json.dumps(schema, ensure_ascii=False, indent=2)


@register_tool  
def define_layer2_parallel_calls() -> str:
    """Phase 2: 第二层并行调用（核心POI信息层）的格式定义"""
    schema = {
        "layer": 2,
        "name": "核心POI信息层",
        "description": "基于目的地坐标，并发获取五大核心业务组的POI信息",
        "business_groups": {
            "停车信息组": {
                "icon": "🅿️",
                "priority": 1,
                "calls": [
                    {
                        "tool": "maps_around_search", 
                        "keywords": ["停车场", "地下停车场"],
                        "purpose": "搜索停车设施"
                    }
                ]
            },
            "充电信息组": {
                "icon": "🔋", 
                "priority": 2,
                "calls": [
                    {
                        "tool": "maps_around_search", 
                        "keywords": ["充电桩", "充电站"],
                        "purpose": "搜索充电设施"
                    }
                ]
            },
            "美食餐饮组": {
                "icon": "🍽️",
                "priority": 3,
                "calls": [
                    {
                        "tool": "maps_around_search", 
                        "keywords": ["美食", "当地特色"],
                        "purpose": "搜索餐饮场所"
                    }
                ]
            },
            "景点娱乐组": {
                "icon": "🎯",
                "priority": 4,
                "calls": [
                    {
                        "tool": "maps_around_search", 
                        "keywords": ["景点"],
                        "purpose": "搜索景点和娱乐设施"
                    }
                ]
            },
            "住宿信息组": {
                "icon": "🏨",
                "priority": 5,
                "calls": [
                    {
                        "tool": "maps_around_search", 
                        "keywords": ["酒店", "民宿"],
                        "purpose": "搜索住宿设施"
                    }
                ]
            }
        }
    }
    return json.dumps(schema, ensure_ascii=False, indent=2)


@register_tool
def define_layer3_parallel_calls() -> str:
    """Phase 2: 第三层并行调用（路线与辅助信息层）的格式定义"""
    schema = {
        "layer": 3,
        "name": "路线与辅助信息层",
        "description": "在获取核心POI后，开始规划具体路线并获取媒体信息",
        "groups": {
            "路线规划组": {
                "icon": "🛣️",
                "calls": [
                    {
                        "tool": "maps_direction_driving", 
                        "purpose": "获取主干路线，解析过路费明细、预估耗时、路况"
                    },
                    {
                        "tool": "maps_direction_walking", 
                        "purpose": "获取步行接驳路线（停车场到景点）"
                    }
                ]
            },
            "图片媒体组": {
                "icon": "📸", 
                "calls": [
                    {
                        "purpose": "汇总所有maps_search_detail中的高质量官方图片和用户上传图片",
                        "requirement": "确保每个POI都有视觉参考"
                    }
                ]
            }
        }
    }
    return json.dumps(schema, ensure_ascii=False, indent=2)


@register_tool
def define_layer4_parallel_calls() -> str:
    """Phase 2: 第四层并行调用（深度信息挖掘层）的格式定义"""
    schema = {
        "layer": 4,
        "name": "深度信息挖掘层",
        "description": "进行更深层次的个性化挖掘和应急信息准备",
        "groups": {
            "个性化深度挖掘组": {
                "icon": "🎯",
                "calls": [
                    {
                        "tool": "maps_around_search", 
                        "keywords": "根据用户画像标签动态生成",
                        "examples": ["母婴室", "拍照打卡地", "无障碍设施"],
                        "purpose": "根据用户标签（如'亲子'、'摄影'）搜索特定设施"
                    }
                ]
            },
            "应急便民信息组": {
                "icon": "🚨",
                "calls": [
                    {
                        "tool": "maps_around_search", 
                        "keywords": ["医院", "药店", "公共厕所"],
                        "purpose": "搜索应急和便民设施"
                    }
                ]
            }
        }
    }
    return json.dumps(schema, ensure_ascii=False, indent=2)


# === Phase 3-4 工具在 format_tools_phase3.py 中定义 ===
# 这里只保留 Phase 1-2 的工具，避免重复注册 