"""
V2旅行规划端到端测试

验证从用户请求到收到完整规划结果的整个事件驱动业务流程。
测试Redis Pub/Sub架构、NotificationService和完整的节点执行流。
"""

import asyncio
import json
import pytest
import time
from typing import List, Dict, Any
from unittest.mock import patch, AsyncMock

import httpx

from src.database.redis_client import RedisClient
from src.services.notification_service import NotificationService
from src.agents.travel_planner_lg.nodes import (
    analyze_core_intent, analyze_multi_city_strategy, analyze_driving_context,
    analyze_preferences, execute_planning_stage, finalize_result
)
from src.agents.travel_planner_lg.state import TravelPlanState


class TestTravelPlannerV2E2E:
    """V2旅行规划端到端测试类"""

    @pytest.fixture
    async def redis_client(self):
        """真实Redis客户端（需要运行中的Redis服务）"""
        redis_client = RedisClient()
        await redis_client.connect()
        yield redis_client
        await redis_client.disconnect()

    @pytest.fixture
    async def notification_service(self, redis_client):
        """NotificationService实例"""
        return NotificationService(redis_client, task_ttl=300)  # 5分钟TTL用于测试

    @pytest.fixture
    def sample_request(self):
        """示例规划请求"""
        return {
            "user_id": "test_user_123",
            "query": "我想去上海玩两天，喜欢历史文化景点"
        }

    @pytest.mark.asyncio
    async def test_complete_planning_workflow(self, notification_service, sample_request):
        """测试完整的规划工作流程"""
        
        task_id = f"e2e_test_{int(time.time())}"
        
        try:
            # 1. 初始化任务
            await notification_service.initialize_task(task_id)
            
            # 验证任务状态
            status = await notification_service.get_task_status(task_id)
            assert status is not None
            assert status["overall_status"] == "pending"
            assert status["current_step"] == "initialization"
            
            # 2. 创建初始状态
            initial_state: TravelPlanState = {
                "trace_id": task_id,
                "task_id": task_id,
                "user_id": sample_request["user_id"],
                "original_query": sample_request["query"],
                "notification_service": notification_service,
                "execution_mode": "automatic",
                "destinations": [],
                "multi_city_strategy": None,
                "core_intent": None,
                "attraction_preferences": None,
                "food_preferences": None,
                "accommodation_preferences": None,
                "user_profile": None,
                "current_narration_text": None,
                "user_vehicle_info": None,
                "driving_strategy": "general_assistance",
                "planning_range_km": None,
                "range_buffer_factor": 0.8,
                "city_plan_results": [],
                "planned_charging_stops": None,
                "driving_routes": None,
                "orchestrated_itinerary": None,
                "final_itinerary": None,
                "user_feedback": None,
                "error": None,
                "clarification_needed": None,
                "current_step": "初始化",
                "progress_percentage": 0,
                "tool_calls_log": [],
                "retrieved_memories": None,
                "new_memories": None
            }
            
            # 3. 执行节点序列并验证每个步骤
            
            # 步骤1: 核心意图分析
            result1 = await analyze_core_intent(initial_state)
            assert "destinations" in result1
            assert result1["destinations"]  # 应该识别出目的地
            assert "core_intent" in result1
            assert result1["progress_percentage"] == 20
            
            # 验证Redis状态更新
            status = await notification_service.get_task_status(task_id)
            assert status["overall_status"] == "running"
            assert status["current_step"] == "core_intent_analysis"
            
            # 更新状态
            initial_state.update(result1)
            
            # 步骤2: 多城市策略分析
            result2 = await analyze_multi_city_strategy(initial_state)
            assert result2["progress_percentage"] == 30
            initial_state.update(result2)
            
            # 步骤3: 驾驶情境分析
            result3 = await analyze_driving_context(initial_state)
            initial_state.update(result3)
            
            # 步骤4: 偏好分析
            result4 = await analyze_preferences(initial_state)
            initial_state.update(result4)
            
            # 步骤5: 执行规划阶段
            result5 = await execute_planning_stage(initial_state)
            initial_state.update(result5)
            
            # 步骤6: 最终化结果
            result6 = await finalize_result(initial_state)
            initial_state.update(result6)
            
            # 4. 验证最终结果
            final_status = await notification_service.get_task_status(task_id)
            assert final_status["overall_status"] == "completed"
            assert "final_result" in final_status
            
            # 验证最终行程数据
            final_result = final_status["final_result"]
            assert "itinerary" in final_result or "orchestrated_itinerary" in final_result
            
            print(f"✅ 端到端测试成功完成，任务ID: {task_id}")
            
        finally:
            # 清理测试数据
            await notification_service.cleanup_task(task_id)

    @pytest.mark.asyncio
    async def test_event_sequence_validation(self, redis_client, sample_request):
        """测试事件序列的正确性"""
        
        task_id = f"event_test_{int(time.time())}"
        collected_events = []
        
        # 设置事件监听器
        async def event_listener():
            channel = f"task_channel:{task_id}"
            pubsub = redis_client.client.pubsub()
            await pubsub.subscribe(channel)
            
            try:
                while True:
                    message = await pubsub.get_message(ignore_subscribe_messages=True, timeout=10)
                    if message and message['data']:
                        event_data = json.loads(message['data'])
                        collected_events.append(event_data)
                        
                        # 如果收到EOS事件，结束监听
                        if event_data.get('event') == 'eos':
                            break
            finally:
                await pubsub.unsubscribe(channel)
        
        # 同时启动事件监听和任务执行
        listener_task = asyncio.create_task(event_listener())
        
        try:
            # 模拟任务执行
            notification_service = NotificationService(redis_client, task_ttl=300)
            await notification_service.initialize_task(task_id)
            
            # 模拟几个步骤
            await notification_service.notify_step_start(
                task_id, "core_intent_analysis", "解析用户需求", "正在分析旅行意图..."
            )
            await asyncio.sleep(0.1)
            
            await notification_service.notify_step_end(
                task_id, "core_intent_analysis", "success", {
                    "content": "识别出目的地：上海",
                    "destinations": ["上海"]
                }
            )
            await asyncio.sleep(0.1)
            
            await notification_service.notify_step_start(
                task_id, "multi_city_strategy", "多城市策略", "分析多城市策略..."
            )
            await asyncio.sleep(0.1)
            
            await notification_service.notify_step_end(
                task_id, "multi_city_strategy", "success", {
                    "content": "单目的地，无需多城市策略"
                }
            )
            await asyncio.sleep(0.1)
            
            # 完成任务
            await notification_service.notify_final_result(task_id, {
                "itinerary": {
                    "title": "上海2日游",
                    "days": 2,
                    "destinations": ["上海"]
                }
            })
            
            # 等待事件监听器完成
            await asyncio.wait_for(listener_task, timeout=5)
            
        except asyncio.TimeoutError:
            listener_task.cancel()
        
        # 验证事件序列
        assert len(collected_events) >= 5  # 至少应该有开始、结束、完成、EOS事件
        
        # 验证事件类型序列
        event_types = [event.get('event') for event in collected_events]
        assert 'step_start' in event_types
        assert 'step_end' in event_types  
        assert 'complete' in event_types
        assert 'eos' in event_types
        
        # 验证事件数据结构
        for event in collected_events:
            assert 'event' in event
            if event['event'] in ['step_start', 'step_end']:
                assert 'data' in event
                assert 'step_name' in event['data']
        
        print(f"✅ 事件序列验证成功，收集到{len(collected_events)}个事件")
        
        # 清理
        await notification_service.cleanup_task(task_id)

    @pytest.mark.asyncio
    async def test_api_sse_integration(self, sample_request):
        """测试API的SSE集成（模拟测试）"""
        
        # 这个测试需要运行的应用服务器，在CI/CD中可能需要跳过
        pytest.skip("需要运行中的应用服务器进行真实的HTTP测试")
        
        base_url = "http://localhost:8000"  # 假设应用运行在8000端口
        
        async with httpx.AsyncClient() as client:
            try:
                # 发送V2 API请求
                response = await client.post(
                    f"{base_url}/api/travel/v2/plan/stream",
                    json=sample_request,
                    timeout=30
                )
                
                assert response.status_code == 200
                assert response.headers["content-type"] == "text/plain; charset=utf-8"
                
                # 读取SSE流
                events = []
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        try:
                            event_data = json.loads(line[6:])
                            events.append(event_data)
                            
                            # 如果收到EOS，停止读取
                            if event_data.get('event') == 'eos':
                                break
                        except json.JSONDecodeError:
                            continue
                
                # 验证收到的事件
                assert len(events) > 0
                event_types = [event.get('event') for event in events]
                assert 'step_start' in event_types or 'complete' in event_types
                
                print(f"✅ API SSE集成测试成功，收到{len(events)}个事件")
                
            except httpx.ConnectError:
                pytest.skip("无法连接到应用服务器")

    @pytest.mark.asyncio 
    async def test_error_handling_workflow(self, notification_service):
        """测试错误处理工作流"""
        
        task_id = f"error_test_{int(time.time())}"
        
        try:
            # 初始化任务
            await notification_service.initialize_task(task_id)
            
            # 模拟步骤开始
            await notification_service.notify_step_start(
                task_id, "test_step", "测试步骤", "正在执行测试..."
            )
            
            # 模拟步骤错误
            error_message = "模拟的测试错误"
            await notification_service.notify_error(task_id, error_message, "test_step")
            
            # 验证错误状态
            status = await notification_service.get_task_status(task_id)
            assert status["overall_status"] == "failed"
            assert status["error_info"] == error_message
            assert status["current_step"] == "test_step"
            
            print(f"✅ 错误处理工作流验证成功")
            
        finally:
            await notification_service.cleanup_task(task_id)

    @pytest.mark.asyncio
    async def test_task_status_persistence(self, notification_service):
        """测试任务状态持久化"""
        
        task_id = f"persistence_test_{int(time.time())}"
        
        try:
            # 初始化任务
            await notification_service.initialize_task(task_id)
            
            # 执行一系列步骤
            steps = [
                ("step1", "第一步", "执行第一步..."),
                ("step2", "第二步", "执行第二步..."),
                ("step3", "第三步", "执行第三步...")
            ]
            
            for step_name, title, message in steps:
                await notification_service.notify_step_start(task_id, step_name, title, message)
                await notification_service.notify_step_end(task_id, step_name, "success", {
                    "content": f"{title}完成"
                })
            
            # 验证状态持久化
            status = await notification_service.get_task_status(task_id)
            assert status["overall_status"] == "running"
            
            steps_status = status["steps_status"]
            assert len(steps_status) == 3
            
            for step_name, _, _ in steps:
                assert step_name in steps_status
                assert steps_status[step_name]["status"] == "success"
                assert "start_time" in steps_status[step_name]
                assert "end_time" in steps_status[step_name]
            
            print(f"✅ 任务状态持久化验证成功")
            
        finally:
            await notification_service.cleanup_task(task_id)

    def test_event_format_compliance(self):
        """测试事件格式符合推送.md文档要求"""
        
        # 测试step_start事件格式
        step_start_event = {
            "event": "step_start",
            "data": {
                "step_id": "core_intent_analysis_test123",
                "step_name": "core_intent_analysis", 
                "title": "解析用户需求",
                "message": "正在分析您的旅行意图..."
            }
        }
        
        assert step_start_event["event"] == "step_start"
        assert "data" in step_start_event
        assert "step_id" in step_start_event["data"]
        assert "step_name" in step_start_event["data"]
        assert "title" in step_start_event["data"]
        assert "message" in step_start_event["data"]
        
        # 测试step_end事件格式
        step_end_event = {
            "event": "step_end",
            "data": {
                "step_id": "core_intent_analysis_test123",
                "step_name": "core_intent_analysis",
                "status": "success",
                "result": {"content": "识别出目的地：上海"}
            }
        }
        
        assert step_end_event["event"] == "step_end"
        assert step_end_event["data"]["status"] in ["success", "failed"]
        assert "result" in step_end_event["data"]
        
        # 测试complete事件格式
        complete_event = {
            "event": "complete",
            "data": {
                "itinerary": {
                    "title": "上海2日游",
                    "days": 2
                }
            }
        }
        
        assert complete_event["event"] == "complete"
        assert "data" in complete_event
        
        # 测试error事件格式
        error_event = {
            "event": "error",
            "data": {
                "step_name": "core_intent_analysis",
                "message": "LLM服务超时"
            }
        }
        
        assert error_event["event"] == "error"
        assert "step_name" in error_event["data"]
        assert "message" in error_event["data"]
        
        # 测试eos事件格式
        eos_event = {"event": "eos"}
        assert eos_event["event"] == "eos"
        
        print(f"✅ 事件格式符合性验证成功")


if __name__ == "__main__":
    # 可以单独运行的快速测试
    async def quick_test():
        print("🚀 启动V2端到端快速测试...")
        
        # 这里可以添加一些不需要外部依赖的基础测试
        test_instance = TestTravelPlannerV2E2E()
        test_instance.test_event_format_compliance()
        
        print("✅ 快速测试完成")
    
    asyncio.run(quick_test()) 