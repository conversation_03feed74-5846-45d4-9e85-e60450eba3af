"""
直接测试多城市策略节点
验证多目的地时的LLM调用
"""

import asyncio
import time
from src.agents.travel_planner_lg.nodes import analyze_multi_city_strategy
from src.agents.travel_planner_lg.state import TravelPlanState

async def test_multi_city_node_directly():
    """直接测试多城市策略节点"""
    print("🏙️ 直接测试多城市策略节点")
    print("=" * 50)
    
    # 测试用例1: 两个城市
    print("\n1️⃣ 测试两个城市")
    print("-" * 30)
    
    state_2_cities = {
        "trace_id": "test_multi_direct_001",
        "task_id": "test_multi_direct_001",
        "user_id": "test_user",
        "original_query": "我想去北京和上海旅游5天，先去北京2天看历史文化，再去上海3天体验现代都市",
        "destinations": ["北京", "上海"],  # 两个目的地
        "core_intent": {
            "destinations": ["北京", "上海"],
            "days": 5,
            "interests": ["历史文化", "现代都市"]
        },
        "execution_mode": "automatic",
        "notification_service": None
    }
    
    start_time = time.time()
    try:
        result_2 = await analyze_multi_city_strategy(state_2_cities)
        execution_time = time.time() - start_time
        
        print(f"⏱️  执行时间: {execution_time:.2f}秒")
        print(f"📋 当前步骤: {result_2.get('current_step', 'N/A')}")
        print(f"🗺️ 策略: {result_2.get('multi_city_strategy', 'N/A')}")
        print(f"📝 说明: {result_2.get('current_narration_text', 'N/A')}")
        
        if execution_time < 0.5:
            print("⚡ 执行过快，可能是硬编码")
        else:
            print("🤖 执行时间正常，可能使用了LLM")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
    
    # 测试用例2: 三个城市
    print("\n2️⃣ 测试三个城市")
    print("-" * 30)
    
    state_3_cities = {
        "trace_id": "test_multi_direct_002",
        "task_id": "test_multi_direct_002", 
        "user_id": "test_user",
        "original_query": "我想去北京、西安、成都三个城市旅游7天，喜欢历史文化和美食",
        "destinations": ["北京", "西安", "成都"],  # 三个目的地
        "core_intent": {
            "destinations": ["北京", "西安", "成都"],
            "days": 7,
            "interests": ["历史文化", "美食"]
        },
        "execution_mode": "automatic",
        "notification_service": None
    }
    
    start_time = time.time()
    try:
        result_3 = await analyze_multi_city_strategy(state_3_cities)
        execution_time = time.time() - start_time
        
        print(f"⏱️  执行时间: {execution_time:.2f}秒")
        print(f"📋 当前步骤: {result_3.get('current_step', 'N/A')}")
        print(f"🗺️ 策略: {result_3.get('multi_city_strategy', 'N/A')}")
        print(f"📝 说明: {result_3.get('current_narration_text', 'N/A')}")
        
        if execution_time < 0.5:
            print("⚡ 执行过快，可能是硬编码")
        else:
            print("🤖 执行时间正常，可能使用了LLM")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
    
    # 测试用例3: 单个城市（对比）
    print("\n3️⃣ 测试单个城市（对比）")
    print("-" * 30)
    
    state_1_city = {
        "trace_id": "test_multi_direct_003",
        "task_id": "test_multi_direct_003",
        "user_id": "test_user", 
        "original_query": "我想去北京旅游3天，喜欢历史文化",
        "destinations": ["北京"],  # 单个目的地
        "core_intent": {
            "destinations": ["北京"],
            "days": 3,
            "interests": ["历史文化"]
        },
        "execution_mode": "automatic",
        "notification_service": None
    }
    
    start_time = time.time()
    try:
        result_1 = await analyze_multi_city_strategy(state_1_city)
        execution_time = time.time() - start_time
        
        print(f"⏱️  执行时间: {execution_time:.2f}秒")
        print(f"📋 当前步骤: {result_1.get('current_step', 'N/A')}")
        print(f"🗺️ 策略: {result_1.get('multi_city_strategy', 'N/A')}")
        print(f"📝 说明: {result_1.get('current_narration_text', 'N/A')}")
        
        if execution_time < 0.5:
            print("⚡ 执行过快，符合预期（单城市应该快速跳过）")
        else:
            print("🤖 执行时间较长，可能有问题")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

async def main():
    """主函数"""
    print("🧪 多城市策略节点直接测试")
    print("验证多目的地时是否调用LLM进行策略分析\n")
    
    await test_multi_city_node_directly()
    
    print("\n" + "=" * 50)
    print("📊 测试总结")
    print("=" * 50)
    print("✅ 单城市：应该快速跳过（< 0.5秒）")
    print("🤖 多城市：应该调用LLM分析（> 1秒）")
    print("\n如果多城市测试执行时间过短，说明存在硬编码问题")

if __name__ == "__main__":
    asyncio.run(main())
