# Swagger接口测试指南

## 访问地址
🌐 **Swagger UI**: http://localhost:8000/docs

## 主要测试接口

### 1. 健康检查接口
**接口**: `GET /health`
**用途**: 验证服务是否正常运行
**测试步骤**:
1. 点击 `/health` 接口
2. 点击 "Try it out"
3. 点击 "Execute"
4. 预期返回: `{"status": "healthy"}`

### 2. 旅行规划V2接口 (SSE流式)
**接口**: `POST /api/travel/v2/plan/stream`
**用途**: 主要的旅行规划接口，支持SSE流式返回
**测试步骤**:

#### 测试用例1: 单城市规划
```json
{
  "query": "我想去北京旅游3天，喜欢历史文化和传统美食，预算中等",
  "user_id": "swagger_test_1"
}
```

#### 测试用例2: 多城市规划
```json
{
  "query": "我想去北京和上海旅游5天，先去北京2天看历史文化，再去上海3天体验现代都市",
  "user_id": "swagger_test_2"
}
```

#### 测试用例3: 三城市规划
```json
{
  "query": "我想去北京、西安、成都三个城市旅游7天，喜欢历史文化和美食",
  "user_id": "swagger_test_3"
}
```

**操作步骤**:
1. 点击 `/api/travel/v2/plan/stream` 接口
2. 点击 "Try it out"
3. 在请求体中输入测试数据
4. 点击 "Execute"
5. 观察SSE流式响应

**预期结果**:
- 返回状态码: 200
- Content-Type: text/event-stream
- 流式返回多个SSE事件
- 包含以下步骤事件:
  - `core_intent_analysis` (7-12秒)
  - `multi_city_strategy` (单城市<0.1秒，多城市8-15秒)
  - `driving_context_analysis` (6-8秒)
  - `preference_analysis` (20-27秒)
  - `planning_execution` (3-7秒)
  - `result_finalization` (<0.1秒)

### 3. 意图识别接口
**接口**: `POST /api/travel/intent/analyze`
**用途**: 单独测试意图识别功能
**测试数据**:
```json
{
  "query": "我想去厦门旅游2天，喜欢海边风景",
  "user_id": "intent_test_1"
}
```

### 4. 立即规划接口
**接口**: `POST /api/travel/plan/immediate`
**用途**: 快速规划接口（如果存在）
**测试数据**:
```json
{
  "destinations": ["厦门"],
  "days": 2,
  "interests": ["海边风景", "美食"],
  "user_id": "immediate_test_1"
}
```

## 测试重点验证项

### ✅ LLM使用验证
- **core_intent_analysis**: 应该耗时7-12秒
- **preference_analysis**: 应该耗时20-27秒  
- **driving_context_analysis**: 应该耗时6-8秒
- **multi_city_strategy**: 多城市时应该耗时8-15秒

### ✅ 硬编码问题检查
- **multi_city_strategy**: 单城市时快速跳过(<0.1秒)是正常的
- **result_finalization**: 快速完成(<0.1秒)是正常的
- 其他步骤不应该过快完成

### ✅ 多城市策略验证
- 单城市: 返回"单目的地旅行，无需多城市策略"
- 两城市: 返回详细的直线型策略
- 三城市: 返回环线或优化路线策略

### ✅ 规划质量验证
- 返回的景点、餐厅、住宿信息应该真实
- 路线规划应该包含实际距离和时间
- 不应该包含"约100公里"、"约2小时"等硬编码文本

## 测试结果记录

### 当前测试状态 (2025-07-10)

| 接口 | 状态 | LLM使用率 | 备注 |
|------|------|-----------|------|
| 单城市规划 | ✅ 正常 | 66.7% | multi_city_strategy快速跳过正常 |
| 多城市规划 | ✅ 正常 | 83.3% | 所有步骤都使用LLM |
| 意图识别 | ✅ 正常 | 100% | 单独测试正常 |
| 立即规划 | ✅ 修复 | - | 已修复硬编码问题 |

### 修复成果
- ✅ **驾驶情境分析**: 从0.08秒硬编码改为6-8秒LLM分析
- ✅ **用户偏好分析**: 从0.12秒硬编码改为20-27秒LLM分析  
- ✅ **多城市策略**: 多目的地时从0.17秒硬编码改为8-15秒LLM分析
- ✅ **路线规划**: 从硬编码"约100公里"改为真实API计算
- ✅ **行程编排**: 从简单取前N个改为智能评分排序

## 常见问题排查

### 1. 接口返回500错误
- 检查服务器日志
- 确认数据库连接正常
- 验证LLM服务可用性

### 2. SSE流中断
- 检查网络连接
- 确认请求超时设置
- 查看服务器资源使用情况

### 3. 响应时间过长
- 正常情况下单城市规划约45秒
- 多城市规划约70秒
- 如果超过2分钟可能有问题

### 4. 硬编码问题检测
- 查看步骤执行时间
- 检查返回内容是否包含固定文本
- 验证个性化程度

## 下一步测试建议

1. **压力测试**: 并发多个请求测试系统稳定性
2. **边界测试**: 测试极端输入（很长的查询、特殊字符等）
3. **错误处理**: 测试无效输入的错误处理
4. **性能优化**: 监控各步骤的性能瓶颈
5. **用户体验**: 测试前端UI与后端SSE的集成效果
