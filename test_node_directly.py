#!/usr/bin/env python3
"""
直接测试LangGraph节点功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.agents.travel_planner_langgraph.nodes import core_intent_analyzer_node
from src.agents.travel_planner_langgraph.state import TravelPlanState

async def test_core_intent_analyzer_node():
    """直接测试核心意图分析节点"""
    print("🧪 直接测试核心意图分析节点...")
    
    # 创建测试状态
    test_state = TravelPlanState(
        session_id="test_session_123",
        user_id="1",  # 使用数据库中存在的用户
        original_query="上海3天亲子慢节奏趣味性儿童友好",
        planning_mode="interactive",
        processing_stage="intent_analysis",
        events=[],
        tool_calls=[],
        api_calls=[],
        has_error=False,
        error_message=None,
        last_event_id=0  # 添加缺失的字段
    )
    
    print(f"📝 测试查询: {test_state['original_query']}")
    print(f"👤 用户ID: {test_state['user_id']}")
    
    try:
        # 调用节点
        result_state = await core_intent_analyzer_node(test_state)
        
        print("✅ 节点执行成功")
        
        # 检查结果
        if "comprehensive_user_profile" in result_state:
            profile = result_state["comprehensive_user_profile"]
            print(f"✅ 获取到用户综合画像")
            print(f"   用户ID: {profile.get('user_id')}")
            print(f"   画像完整度: {profile.get('profile_completeness', 0):.2f}")
            print(f"   用户摘要: {'有' if profile.get('user_summary') else '无'}")
            print(f"   用户记忆: {len(profile.get('user_memories', []))} 条")
            print(f"   旅行历史: {len(profile.get('travel_history', []))} 条")
            
            # 检查是否有真实的数据库数据
            if profile.get('user_summary'):
                summary = profile['user_summary']['summary']
                print(f"   摘要内容: {summary[:100]}...")
                
                # 检查是否包含李伟的特征信息
                if "李伟" in summary or "摄影" in summary or "自驾" in summary:
                    print("✅ 检测到真实的数据库数据！")
                else:
                    print("⚠️  未检测到预期的数据库数据")
        else:
            print("❌ 未获取到用户综合画像")
        
        if "core_intent" in result_state:
            core_intent = result_state["core_intent"]
            print(f"✅ 获取到核心意图分析结果")
            print(f"   目的地: {core_intent.get('destinations', [])}")
            print(f"   天数: {core_intent.get('days')}")
            print(f"   旅行主题: {core_intent.get('travel_theme')}")
        else:
            print("❌ 未获取到核心意图分析结果")
        
        # 检查事件
        events = result_state.get("events", [])
        print(f"✅ 生成了 {len(events)} 个事件")
        for i, event in enumerate(events):
            print(f"   事件 {i+1}: {event.get('event_type')} - {event.get('data', {}).get('message', '')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 节点执行失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 启动LangGraph节点直接测试...")
    print("=" * 60)
    
    success = await test_core_intent_analyzer_node()
    
    if success:
        print("\n🎉 节点测试成功！")
        print("✅ 数据库驱动的用户画像功能正常")
        print("✅ 核心意图分析节点正常工作")
        return True
    else:
        print("\n💥 节点测试失败！")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    
    if success:
        print("\n🎯 测试结论: LangGraph节点功能正常！")
        sys.exit(0)
    else:
        print("\n💥 测试结论: LangGraph节点存在问题")
        sys.exit(1)
