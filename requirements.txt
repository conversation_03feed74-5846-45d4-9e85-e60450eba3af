# Core Framework Dependencies
autogen-agentchat>=0.4.0
autogen-core>=0.4.0
autogen-ext>=0.4.0

# Web Framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
flask==3.0.0

# Data Processing & Serialization
pydantic>=2.5.0
pydantic-settings>=2.1.0
dataclasses-json==0.6.4
pyyaml>=6.0.1

# HTTP & API
httpx>=0.25.0
requests~=2.32.3
sse-starlette>=0.15.0
aiohttp
websockets>=10.0,<13.0.0

# Database Dependencies
# LangGraph dependencies
langgraph>=0.2.0
langchain>=0.3.0
langchain-core>=0.3.0
langchain-openai>=0.2.0

# Database
sqlalchemy>=2.0.0
sqlalchemy[asyncio]>=2.0.0
pymysql>=1.1.0
pymongo>=4.6.0
motor>=3.3.0
aiomysql>=0.2.0
redis>=5.0.0

# AI & LLM
openai>=1.0.0
tiktoken>=0.5.0

# Security & Encryption
pycryptodome~=3.22.0
crypto~=1.4.1

# Utilities
python-dotenv>=1.0.0
jinja2>=3.1.0
structlog>=23.2.0

# MCP (Model Context Protocol)
mcp>=1.0.0

# Development & Testing (Optional)
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.12.0
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.6.0
pre-commit>=3.5.0

# MinIO
minio>=7.1.15
aiofiles