# 前端界面重构设计文档 (Frontend UI Refactoring Guide)

## 1. 概述

本文档旨在为前端界面的现代化重构提供一份详细的设计与开发指南。本次重构的核心目标是，将现有UI升级为与新版 `TravelPlannerAgent` (基于LangGraph) 完全匹配的、支持两阶段交互模式的现代化用户界面。

### 1.1. 核心设计理念

*   **透明化 (Transparency)**: 向用户清晰地展示AI的"思考过程"，即它是如何一步步分析用户需求的。
*   **流式交互 (Streaming Interaction)**: 行程的构建过程是动态、可视的，用户可以实时看到行程卡片被逐一添加到界面上。
*   **两阶段体验 (Two-Phase Experience)**:
    1.  **阶段一：交互式分析 (Interactive Analysis)**: 对应UI左侧的分析面板。
    2.  **阶段二：流式行程构建 (Streaming Itinerary Construction)**: 对应UI右侧的行程面板。

## 2. 界面布局与核心组件

新版UI采用清晰的双栏布局。

```ascii
+--------------------------------------------------------------------------------------------------+
| [用户查询: 我想要带孩子去上海玩三天，帮我规划一份行程]                                           |
+------------------------------------------------------+-----------------------------------------+
|                                                      |                                         |
|  [左栏: 分析面板]                                    |  [右栏: 行程与状态面板]                 |
|                                                      |                                         |
|  (✔) 解析用户需求和画像                              |  [阶段一: 状态/引导文本]                |
|      上海3天|亲子|慢节奏...                         |  正在为您规划上海3天亲子出行的行程...     |
|                                                      |                                         |
|  (✔) 景点偏好类型                                    |-----------------------------------------|
|      景点推荐: 乐园/休闲/                            |  [阶段二: 流式行程卡片]                 |
|                                                      |                                         |
|  (✔) 美食偏好                                        |  DAY 1 梦幻童话日                       |
|      不辣、口碑老店...                               |   +------------------+                  |
|                                                      |   | [图片] 上海迪士尼  |                  |
|  (O) 住宿偏好                                        |   +------------------+                  |
|      连锁1800-1500元...                              |                                         |
|                                                      |                                         |
+------------------------------------------------------+-----------------------------------------+
|                                                      |                                         |
|  [              立即规划              ]             | [取消生成] [重新生成] [查看行程]        |
|                                                      |                                         |
+------------------------------------------------------+-----------------------------------------+
```

![新UI设计](https://i.imgur.com/7bkl0gL.png)

*   **左栏 (Analysis Panel)**: 负责展示 **阶段一** 的分析结果。
*   **右栏 (Itinerary & Status Panel)**: 负责展示 **阶段二** 的行程构建过程与结果。

### 2.1. 左栏：分析面板

*   **功能**: 逐项展示Agent对用户需求的分析结论，建立用户信任感。
*   **组成**: 由多个分析项目组成，每个项目包含标题和结论。
    *   解析用户需求和画像
    *   景点偏好
    *   美食偏好
    *   住宿偏好
*   **状态**: 每个分析项都有两种状态，由图标表示：
    *   `加载中` (如转动的Spinner): 表示Agent正在分析此项。
    *   `已完成` (如绿色的Checkmark): 表示分析已完成，结论已显示。

### 2.2. 右栏：行程与状态面板

*   **功能**: 根据Agent所处的不同阶段，展示不同的内容。
*   **阶段一 (分析中)**: 显示引导性或等待性的信息。例如："正在为您规划上海3天亲子出行的行程..."。
*   **阶段二 (规划中)**: 动态、流式地展示生成的行程结果。
    *   **每日行程 (Day-by-Day)**: 以"DAY 1"、"DAY 2"为单位，组织行程内容。
    *   **POI信息卡片**: 每个卡片代表一个POI（景点、餐厅、酒店），包含关键信息：
        *   图片
        *   名称
        *   类型标签 (如"景点"、"美食")
        *   关键摘要/特色 (如"亲子必玩"、"酒店距迪士尼5分钟"、"人均¥100-200")

### 2.3. 控制按钮

*   **立即规划**: 在分析阶段完成后激活。点击后，通知后端开始进入 **阶段二** 的后台规划。
*   **取消生成**: 在任务进行中，允许用户中止当前的规划任务。
*   **重新生成**: 允许用户废弃当前结果，使用相同或新的输入重新开始一次规划。
*   **查看行程**: 在规划完全结束后激活，用于跳转到完整的行程详情页面。

## 3. 前端与后端的SSE通信契约

这是本次前后端协同开发的核心。前端通过监听一个SSE (Server-Sent Events) 端点，根据接收到的不同`event_type`来更新UI的不同部分。

所有事件的`payload`（数据载荷）都遵循技术方案 `TravelPlannerAgent_LangGraph_Refactoring_Plan.md` 中定义的Pydantic模型。

---

### **事件: `ANALYSIS_STEP`**
*   **时机**: 在 **阶段一**，每当Agent完成一项分析时触发。
*   **Payload**: `AnalysisStepPayload`
    ```json
    {
      "title": "景点偏好",
      "content": {"类型": "乐园/休闲", "特点": "深度游、时间节奏慢"},
      "narration_text": "..."
    }
    ```
*   **UI行为**:
    1.  在 **左侧分析面板** 找到与`payload.title`匹配的分析项。
    2.  将该项的图标从"加载中"更新为"已完成"。
    3.  将`payload.content`的内容渲染并显示出来。

---

### **事件: `PLANNING_PROGRESS`**
*   **时机**: 在 **阶段二**，每当后台完成一个关键规划步骤时触发。
*   **Payload**: `PlanningProgressPayload`
    ```json
    {
      "step_name": "正在规划景点信息...",
      "progress": 35
    }
    ```
*   **UI行为**:
    1.  在 **右侧行程面板** 的某个位置（如顶部），更新进度提示文本。
    2.  （可选）可以根据`progress`百分比驱动一个进度条。

---

### **事件: `ITINERARY_UPDATE`**
*   **时机**: 在 **阶段二**，每当生成了一部分可供展示的行程时触发。这是实现流式效果的核心事件。
*   **Payload**: `ItineraryUpdatePayload`
    ```json
    {
      "day": 1,
      "activities": [
        {
          "name": "上海迪士尼乐园",
          "type": "景点",
          "image_urls": ["..."],
          "summary": "亲子必玩"
        }
      ],
      "meals": []
    }
    ```
*   **UI行为**:
    1.  在 **右侧行程面板** 找到对应的`DAY {payload.day}`容器。
    2.  根据`payload.activities`或`payload.meals`中的数据，创建并追加新的POI信息卡片。

---

### **事件: `FINAL_ITINERARY`**
*   **时机**: 在 **阶段二** 的末尾，当所有规划步骤完成时触发。
*   **Payload**: `TravelItinerary` (一个包含所有行程信息的完整JSON对象)
*   **UI行为**:
    1.  前端接收并存储这个完整的行程对象。
    2.  将"取消生成"按钮置灰或变为"已完成"。
    3.  **激活"查看行程"按钮**。

---

### **事件: `ERROR`**
*   **时机**: 在任何阶段，如果后端发生不可恢复的错误，则触发。
*   **Payload**: `ErrorPayload`
    ```json
    {
      "message": "抱歉，暂时无法连接到地图服务，请稍后重试。",
      "details": "Amap API connection timed out."
    }
    ```
*   **UI行为**:
    1.  立即停止所有加载动画。
    2.  向用户显示一个友好的错误提示（Toast、Alert或在界面上显示`payload.message`）。

## 4. 交互流程状态机 (State Machine)

```mermaid
graph TD
    A[等待用户输入] -->|用户提交查询| B(分析中);
    B -->|收到多个ANALYSIS_STEP| B;
    B -->|收到所有ANALYSIS_STEP| C(等待确认);
    C -->|点击[立即规划]| D(规划中);
    C -->|点击[取消]| F(任务结束);
    D -->|收到多个ITINERARY_UPDATE| D;
    D -->|收到FINAL_ITINERARY| E(规划完成);
    D -->|点击[取消生成]| F;
    subgraph 任何阶段
      G(任意状态) -->|收到ERROR| H(错误状态);
    end
```

*   **状态A (等待用户输入)**: 初始状态。
*   **状态B (分析中)**: 用户提交查询后。左侧面板逐项更新，右侧显示等待信息。`立即规划`按钮禁用。
*   **状态C (等待确认)**: 所有`ANALYSIS_STEP`事件接收完毕。`立即规划`按钮激活，等待用户点击。
*   **状态D (规划中)**: 用户点击`立即规划`。右侧面板根据`ITINERARY_UPDATE`事件动态构建行程卡片，并根据`PLANNING_PROGRESS`更新进度提示。
*   **状态E (规划完成)**: 收到`FINAL_ITINERARY`事件，任务正常结束。
*   **状态F (任务结束)**: 用户主动取消或任务完成。
*   **状态H (错误状态)**: 收到`ERROR`事件，流程异常终止。 