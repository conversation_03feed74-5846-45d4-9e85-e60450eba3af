"""
直接测试规划节点的修复情况
验证智能行程编排函数是否正常工作
"""

import asyncio
import json
from src.agents.travel_planner_lg.nodes import _create_intelligent_itinerary, _score_and_sort_pois
from src.agents.services.amap_service import AmapService

async def test_intelligent_itinerary():
    """测试智能行程编排函数"""
    print("🧪 测试智能行程编排函数")
    print("=" * 50)
    
    # 模拟数据
    city = "厦门"
    attractions = [
        {"name": "鼓浪屿", "rating": 4.5, "review_count": 1000, "location": "118.063,24.448"},
        {"name": "南普陀寺", "rating": 4.3, "review_count": 800, "location": "118.100,24.445"},
        {"name": "厦门大学", "rating": 4.4, "review_count": 1200, "location": "118.095,24.433"}
    ]
    
    foods = [
        {"name": "沙茶面", "rating": 4.2, "review_count": 500, "avg_price": 25, "location": "118.080,24.450"},
        {"name": "海蛎煎", "rating": 4.0, "review_count": 300, "avg_price": 30, "location": "118.085,24.445"}
    ]
    
    accommodations = [
        {"name": "厦门五星酒店", "rating": 4.6, "review_count": 600, "address": "市中心商圈", "location": "118.090,24.440"}
    ]
    
    days = 2
    trace_id = "test_trace_001"
    
    try:
        # 创建AmapService实例
        amap_service = AmapService()
        
        # 测试智能行程编排
        print("📅 创建智能行程...")
        itinerary = await _create_intelligent_itinerary(
            city=city,
            attractions=attractions,
            foods=foods,
            accommodations=accommodations,
            days=days,
            amap_service=amap_service,
            trace_id=trace_id
        )
        
        print("✅ 智能行程创建成功!")
        print(f"📋 行程详情:")
        print(json.dumps(itinerary, ensure_ascii=False, indent=2))
        
        # 验证是否还有硬编码问题
        itinerary_str = json.dumps(itinerary, ensure_ascii=False)
        hardcoded_issues = []
        
        if "约100公里" in itinerary_str:
            hardcoded_issues.append("发现硬编码距离: '约100公里'")
        if "约2小时" in itinerary_str:
            hardcoded_issues.append("发现硬编码时间: '约2小时'")
        if "用户当前位置" in itinerary_str:
            hardcoded_issues.append("发现硬编码位置: '用户当前位置'")
        
        if hardcoded_issues:
            print(f"\n⚠️ 发现硬编码问题:")
            for issue in hardcoded_issues:
                print(f"   - {issue}")
        else:
            print(f"\n✅ 未发现硬编码问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

async def test_poi_scoring():
    """测试POI评分函数"""
    print("\n🏆 测试POI评分函数")
    print("=" * 50)
    
    # 模拟景点数据
    attractions = [
        {"name": "普通景点", "rating": 3.5, "review_count": 100},
        {"name": "5A级景区", "rating": 4.8, "review_count": 2000},
        {"name": "博物馆", "rating": 4.2, "review_count": 500},
        {"name": "公园", "rating": 4.0, "review_count": 300}
    ]
    
    try:
        amap_service = AmapService()
        
        # 测试评分排序
        scored_attractions = await _score_and_sort_pois(attractions, "attraction", amap_service)
        
        print("📊 评分结果:")
        for i, poi in enumerate(scored_attractions):
            score = poi.get('_score', 0)
            name = poi.get('name', 'N/A')
            rating = poi.get('rating', 0)
            print(f"   {i+1}. {name} - 评分: {score:.1f} (原始评分: {rating})")
        
        # 验证排序是否正确
        scores = [poi.get('_score', 0) for poi in scored_attractions]
        is_sorted = all(scores[i] >= scores[i+1] for i in range(len(scores)-1))
        
        if is_sorted:
            print("✅ POI排序正确")
        else:
            print("❌ POI排序有问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🔧 规划节点修复验证测试")
    print("验证智能行程编排和POI评分功能\n")
    
    # 测试1: 智能行程编排
    test1_result = await test_intelligent_itinerary()
    
    # 测试2: POI评分
    test2_result = await test_poi_scoring()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    print(f"智能行程编排: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"POI评分排序: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！硬编码问题已修复。")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    asyncio.run(main())
